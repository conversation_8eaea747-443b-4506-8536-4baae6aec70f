import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import {
  Palette,
  Layout,
  Maximize2,
  Minimize2,
  Square,
  Circle,
  Zap,
  Sparkles,
  Grid3X3,
  LayoutGrid,
  Layers,
  Settings2,
} from 'lucide-react';

/**
 * 卡片样式配置接口
 * 专门负责卡片的视觉样式配置
 */
export interface CardStyleConfig {
  /** 卡片尺寸 */
  size: 'small' | 'medium' | 'large' | 'extra-large';
  /** 布局模式 */
  layout: 'compact' | 'standard' | 'detailed' | 'minimal';
  /** 主题风格 */
  theme: 'default' | 'modern' | 'glass' | 'gradient' | 'dark';
  /** 间距设置 */
  spacing: 'tight' | 'normal' | 'loose';
  /** 圆角大小 */
  borderRadius: 'none' | 'small' | 'medium' | 'large' | 'full';
  /** 阴影效果 */
  shadow: 'none' | 'small' | 'medium' | 'large' | 'glow';
  /** 动画效果 */
  animation: 'none' | 'subtle' | 'smooth' | 'bouncy';
  /** 列数设置 */
  columns: 'auto' | '1' | '2' | '3' | '4' | '5' | '6';
}

/**
 * 卡片样式配置模态框属性
 */
interface CardStyleConfigModalProps {
  /** 是否打开 */
  open: boolean;
  /** 打开状态变化回调 */
  onOpenChange: (open: boolean) => void;
  /** 当前样式配置 */
  config: CardStyleConfig;
  /** 配置变化回调 */
  onConfigChange: (config: CardStyleConfig) => void;
}

/**
 * 卡片样式配置模态框组件
 * 专门负责卡片的视觉样式配置，包括大小、主题、阴影、动画等
 */
export const CardStyleConfigModal: React.FC<CardStyleConfigModalProps> = ({
  open,
  onOpenChange,
  config,
  onConfigChange,
}) => {
  // 本地配置状态，用于实时预览
  const [previewConfig, setPreviewConfig] = useState<CardStyleConfig>(config);
  
  // 同步外部配置到预览配置
  React.useEffect(() => {
    setPreviewConfig(config);
  }, [config]);
  
  /**
   * 更新预览配置
   * @param newConfig 新的配置
   */
  const updatePreviewConfig = (newConfig: CardStyleConfig) => {
    setPreviewConfig(newConfig);
  };
  
  /**
   * 应用配置
   */
  const applyConfig = () => {
    onConfigChange(previewConfig);
  };
  
  /**
   * 重置配置
   */
  const resetConfig = () => {
    setPreviewConfig(config);
  };

  /**
   * 配置选项组件
   */
  const ConfigSection: React.FC<{
    title: string;
    icon: React.ReactNode;
    children: React.ReactNode;
  }> = ({ title, icon, children }) => (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        {icon}
        <h3 className="font-medium text-sm">{title}</h3>
      </div>
      {children}
    </div>
  );

  /**
   * 选项按钮组件
   */
  const OptionButton: React.FC<{
    value: string;
    currentValue: string;
    label: string;
    description?: string;
    icon?: React.ReactNode;
    onClick: () => void;
  }> = ({ value, currentValue, label, description, icon, onClick }) => (
    <Button
      variant={value === currentValue ? 'default' : 'outline'}
      className={cn(
        "h-auto p-3 flex flex-col items-center gap-2 text-center",
        value === currentValue && "ring-2 ring-primary/50"
      )}
      onClick={onClick}
    >
      {icon && <div className="text-lg">{icon}</div>}
      <div>
        <div className="font-medium text-xs">{label}</div>
        {description && (
          <div className="text-[10px] text-muted-foreground mt-1">
            {description}
          </div>
        )}
      </div>
    </Button>
  );

  // 预设样式配置
  const stylePresets = [
    {
      name: '紧凑高效',
      description: '适合大量任务',
      config: {
        size: 'small' as const,
        layout: 'compact' as const,
        theme: 'default' as const,
        spacing: 'tight' as const,
        borderRadius: 'small' as const,
        shadow: 'small' as const,
        animation: 'subtle' as const,
        columns: 'auto' as const,
      }
    },
    {
      name: '标准平衡',
      description: '平衡显示效果',
      config: {
        size: 'medium' as const,
        layout: 'standard' as const,
        theme: 'modern' as const,
        spacing: 'normal' as const,
        borderRadius: 'medium' as const,
        shadow: 'medium' as const,
        animation: 'smooth' as const,
        columns: 'auto' as const,
      }
    },
    {
      name: '详细展示',
      description: '完整信息显示',
      config: {
        size: 'large' as const,
        layout: 'detailed' as const,
        theme: 'glass' as const,
        spacing: 'loose' as const,
        borderRadius: 'large' as const,
        shadow: 'large' as const,
        animation: 'smooth' as const,
        columns: 'auto' as const,
      }
    },
    {
      name: '炫酷模式',
      description: '视觉效果丰富',
      config: {
        size: 'medium' as const,
        layout: 'standard' as const,
        theme: 'gradient' as const,
        spacing: 'normal' as const,
        borderRadius: 'large' as const,
        shadow: 'glow' as const,
        animation: 'bouncy' as const,
        columns: 'auto' as const,
      }
    }
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Palette className="w-5 h-5" />
            卡片样式配置
          </DialogTitle>
          <DialogDescription>
            自定义任务卡片的视觉样式，包括大小、主题、阴影、动画等效果
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-6 pr-2">
          {/* 预设样式 */}
          <ConfigSection
            title="快速预设"
            icon={<Zap className="w-4 h-4 text-yellow-500" />}
          >
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {stylePresets.map((preset) => (
                <Button
                  key={preset.name}
                  variant="outline"
                  className="h-auto p-3 flex flex-col items-center gap-2"
                  onClick={() => updatePreviewConfig(preset.config)}
                >
                  <div className="font-medium text-sm">{preset.name}</div>
                  <div className="text-xs text-muted-foreground text-center">
                    {preset.description}
                  </div>
                </Button>
              ))}
            </div>
          </ConfigSection>

          <Separator />

          {/* 卡片尺寸 */}
          <ConfigSection
            title="卡片尺寸"
            icon={<Maximize2 className="w-4 h-4 text-blue-500" />}
          >
            <div className="grid grid-cols-4 gap-2">
              <OptionButton
                value="small"
                currentValue={previewConfig.size}
                label="小"
                description="200px"
                icon={<Minimize2 className="w-4 h-4" />}
                onClick={() => updatePreviewConfig({ ...previewConfig, size: 'small' })}
              />
              <OptionButton
                value="medium"
                currentValue={previewConfig.size}
                label="中"
                description="280px"
                icon={<Square className="w-4 h-4" />}
                onClick={() => updatePreviewConfig({ ...previewConfig, size: 'medium' })}
              />
              <OptionButton
                value="large"
                currentValue={previewConfig.size}
                label="大"
                description="350px"
                icon={<Maximize2 className="w-4 h-4" />}
                onClick={() => updatePreviewConfig({ ...previewConfig, size: 'large' })}
              />
              <OptionButton
                value="extra-large"
                currentValue={previewConfig.size}
                label="超大"
                description="450px"
                icon={<Layers className="w-4 h-4" />}
                onClick={() => updatePreviewConfig({ ...previewConfig, size: 'extra-large' })}
              />
            </div>
          </ConfigSection>

          {/* 布局样式 */}
          <ConfigSection
            title="布局样式"
            icon={<Layout className="w-4 h-4 text-green-500" />}
          >
            <div className="grid grid-cols-4 gap-2">
              <OptionButton
                value="minimal"
                currentValue={previewConfig.layout}
                label="极简"
                description="最少信息"
                onClick={() => updatePreviewConfig({ ...previewConfig, layout: 'minimal' })}
              />
              <OptionButton
                value="compact"
                currentValue={previewConfig.layout}
                label="紧凑"
                description="基础信息"
                onClick={() => updatePreviewConfig({ ...previewConfig, layout: 'compact' })}
              />
              <OptionButton
                value="standard"
                currentValue={previewConfig.layout}
                label="标准"
                description="常用信息"
                onClick={() => updatePreviewConfig({ ...previewConfig, layout: 'standard' })}
              />
              <OptionButton
                value="detailed"
                currentValue={previewConfig.layout}
                label="详细"
                description="完整信息"
                onClick={() => updatePreviewConfig({ ...previewConfig, layout: 'detailed' })}
              />
            </div>
          </ConfigSection>

          {/* 主题风格 */}
          <ConfigSection
            title="主题风格"
            icon={<Palette className="w-4 h-4 text-purple-500" />}
          >
            <div className="grid grid-cols-5 gap-2">
              <OptionButton
                value="default"
                currentValue={previewConfig.theme}
                label="默认"
                onClick={() => updatePreviewConfig({ ...previewConfig, theme: 'default' })}
              />
              <OptionButton
                value="modern"
                currentValue={previewConfig.theme}
                label="现代"
                onClick={() => updatePreviewConfig({ ...previewConfig, theme: 'modern' })}
              />
              <OptionButton
                value="glass"
                currentValue={previewConfig.theme}
                label="玻璃"
                onClick={() => updatePreviewConfig({ ...previewConfig, theme: 'glass' })}
              />
              <OptionButton
                value="gradient"
                currentValue={previewConfig.theme}
                label="渐变"
                onClick={() => updatePreviewConfig({ ...previewConfig, theme: 'gradient' })}
              />
              <OptionButton
                value="dark"
                currentValue={previewConfig.theme}
                label="深色"
                onClick={() => updatePreviewConfig({ ...previewConfig, theme: 'dark' })}
              />
            </div>
          </ConfigSection>

          {/* 其他设置 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 间距设置 */}
            <ConfigSection
              title="间距"
              icon={<Grid3X3 className="w-4 h-4 text-orange-500" />}
            >
              <div className="grid grid-cols-3 gap-2">
                <OptionButton
                  value="tight"
                  currentValue={previewConfig.spacing}
                  label="紧密"
                  onClick={() => updatePreviewConfig({ ...previewConfig, spacing: 'tight' })}
                />
                <OptionButton
                  value="normal"
                  currentValue={previewConfig.spacing}
                  label="正常"
                  onClick={() => updatePreviewConfig({ ...previewConfig, spacing: 'normal' })}
                />
                <OptionButton
                  value="loose"
                  currentValue={previewConfig.spacing}
                  label="宽松"
                  onClick={() => updatePreviewConfig({ ...previewConfig, spacing: 'loose' })}
                />
              </div>
            </ConfigSection>

            {/* 圆角设置 */}
            <ConfigSection
              title="圆角"
              icon={<Circle className="w-4 h-4 text-pink-500" />}
            >
              <div className="grid grid-cols-5 gap-2">
                <OptionButton
                  value="none"
                  currentValue={previewConfig.borderRadius}
                  label="无"
                  onClick={() => updatePreviewConfig({ ...previewConfig, borderRadius: 'none' })}
                />
                <OptionButton
                  value="small"
                  currentValue={previewConfig.borderRadius}
                  label="小"
                  onClick={() => updatePreviewConfig({ ...previewConfig, borderRadius: 'small' })}
                />
                <OptionButton
                  value="medium"
                  currentValue={previewConfig.borderRadius}
                  label="中等"
                  onClick={() => updatePreviewConfig({ ...previewConfig, borderRadius: 'medium' })}
                />
                <OptionButton
                  value="large"
                  currentValue={previewConfig.borderRadius}
                  label="大"
                  onClick={() => updatePreviewConfig({ ...previewConfig, borderRadius: 'large' })}
                />
                <OptionButton
                  value="full"
                  currentValue={previewConfig.borderRadius}
                  label="圆形"
                  onClick={() => updatePreviewConfig({ ...previewConfig, borderRadius: 'full' })}
                />
              </div>
            </ConfigSection>
          </div>

          {/* 视觉效果 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 阴影效果 */}
            <ConfigSection
              title="阴影"
              icon={<Layers className="w-4 h-4 text-gray-500" />}
            >
              <div className="grid grid-cols-2 gap-2">
                <OptionButton
                  value="none"
                  currentValue={previewConfig.shadow}
                  label="无阴影"
                  onClick={() => updatePreviewConfig({ ...previewConfig, shadow: 'none' })}
                />
                <OptionButton
                  value="small"
                  currentValue={previewConfig.shadow}
                  label="轻微"
                  onClick={() => updatePreviewConfig({ ...previewConfig, shadow: 'small' })}
                />
                <OptionButton
                  value="medium"
                  currentValue={previewConfig.shadow}
                  label="标准"
                  onClick={() => updatePreviewConfig({ ...previewConfig, shadow: 'medium' })}
                />
                <OptionButton
                  value="large"
                  currentValue={previewConfig.shadow}
                  label="深度"
                  onClick={() => updatePreviewConfig({ ...previewConfig, shadow: 'large' })}
                />
                <OptionButton
                  value="glow"
                  currentValue={previewConfig.shadow}
                  label="发光"
                  onClick={() => updatePreviewConfig({ ...previewConfig, shadow: 'glow' })}
                />
              </div>
            </ConfigSection>

            {/* 动画效果 */}
            <ConfigSection
              title="动画"
              icon={<Sparkles className="w-4 h-4 text-cyan-500" />}
            >
              <div className="grid grid-cols-2 gap-2">
                <OptionButton
                  value="none"
                  currentValue={previewConfig.animation}
                  label="无动画"
                  onClick={() => updatePreviewConfig({ ...previewConfig, animation: 'none' })}
                />
                <OptionButton
                  value="subtle"
                  currentValue={previewConfig.animation}
                  label="微妙"
                  onClick={() => updatePreviewConfig({ ...previewConfig, animation: 'subtle' })}
                />
                <OptionButton
                  value="smooth"
                  currentValue={previewConfig.animation}
                  label="流畅"
                  onClick={() => updatePreviewConfig({ ...previewConfig, animation: 'smooth' })}
                />
                <OptionButton
                  value="bouncy"
                  currentValue={previewConfig.animation}
                  label="弹性"
                  onClick={() => updatePreviewConfig({ ...previewConfig, animation: 'bouncy' })}
                />
              </div>
            </ConfigSection>
          </div>

          {/* 列数设置 */}
          <ConfigSection
            title="列数设置"
            icon={<LayoutGrid className="w-4 h-4 text-indigo-500" />}
          >
            <div className="grid grid-cols-4 gap-2">
              <OptionButton
                value="auto"
                currentValue={previewConfig.columns}
                label="自动"
                description="响应式"
                onClick={() => updatePreviewConfig({ ...previewConfig, columns: 'auto' })}
              />
              <OptionButton
                value="2"
                currentValue={previewConfig.columns}
                label="2列"
                onClick={() => updatePreviewConfig({ ...previewConfig, columns: '2' })}
              />
              <OptionButton
                value="3"
                currentValue={previewConfig.columns}
                label="3列"
                onClick={() => updatePreviewConfig({ ...previewConfig, columns: '3' })}
              />
              <OptionButton
                value="4"
                currentValue={previewConfig.columns}
                label="4列"
                onClick={() => updatePreviewConfig({ ...previewConfig, columns: '4' })}
              />
              <OptionButton
                value="5"
                currentValue={previewConfig.columns}
                label="5列"
                onClick={() => updatePreviewConfig({ ...previewConfig, columns: '5' })}
              />
              <OptionButton
                value="6"
                currentValue={previewConfig.columns}
                label="6列"
                onClick={() => updatePreviewConfig({ ...previewConfig, columns: '6' })}
              />
            </div>
          </ConfigSection>
        </div>

        {/* 底部操作按钮 */}
        <div className="flex justify-between pt-4 border-t">
          <Button variant="outline" onClick={resetConfig}>
            重置
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button onClick={applyConfig}>
              应用样式
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export type { CardStyleConfig };