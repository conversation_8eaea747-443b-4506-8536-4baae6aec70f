'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  Clock,
  MapPin,
  Building2,
  User,
  Truck,
  AlertCircle,
  CheckCircle2,
  PlayCircle,
  PauseCircle,
  XCircle,
  Target
} from 'lucide-react';

/**
 * 卡片配置接口
 */
interface CardConfig {
  size: 'small' | 'medium' | 'large' | 'extra-large';
  layout: 'compact' | 'standard' | 'detailed' | 'minimal';
  theme: 'default' | 'modern' | 'glass' | 'gradient' | 'dark';
  spacing: 'tight' | 'normal' | 'loose';
  borderRadius: 'none' | 'small' | 'medium' | 'large' | 'full';
  shadow: 'none' | 'small' | 'medium' | 'large' | 'glow';
  animation: 'none' | 'subtle' | 'smooth' | 'bouncy';
  columns: 'auto' | '1' | '2' | '3' | '4' | '5' | '6';
  content: {
    header: {
      show: boolean;
      height: 'compact' | 'normal' | 'expanded';
      showStatus: boolean;
      showProgress: boolean;
      showReminder: boolean;
    };
    body: {
      show: boolean;
      layout: 'info-only' | 'info-progress' | 'progress-only' | 'custom';
      showTaskInfo: boolean;
      showProgressRing: boolean;
      progressPosition: 'center' | 'right' | 'left';
    };
    footer: {
      show: boolean;
      height: 'compact' | 'normal' | 'expanded';
      showVehicleList: boolean;
      vehicleDisplayMode: 'grid' | 'list' | 'compact';
    };
    sidePanel: {
      enabled: boolean;
      width: 'narrow' | 'normal' | 'wide';
      opacity: number;
      showProductionLines: boolean;
      autoExpand: boolean;
      expandDuration: number;
    };
  };
}

/**
 * 卡片配置预览组件属性
 */
interface CardConfigPreviewProps {
  config: CardConfig;
  className?: string;
}

/**
 * 模拟任务数据
 */
const mockTask = {
  id: 'TASK-001',
  taskNumber: 'T20241201-001',
  customerName: '示例客户',
  projectName: '混凝土浇筑项目',
  location: '北京市朝阳区',
  dispatchStatus: 'InProgress' as const,
  progress: 65,
  totalVolume: 120,
  completedVolume: 78,
  vehicles: [
    { id: 'V001', plateNumber: '京A12345', status: 'dispatched' },
    { id: 'V002', plateNumber: '京B67890', status: 'dispatched' },
    { id: 'V003', plateNumber: '京C11111', status: 'pending' }
  ],
  hasReminder: true,
  reminderLevel: 'high' as const
};

/**
 * 获取状态图标和样式
 */
const getStatusInfo = (status: string) => {
  switch (status) {
    case 'New':
      return { icon: AlertCircle, label: '新任务', className: 'bg-blue-100 text-blue-700' };
    case 'InProgress':
      return { icon: PlayCircle, label: '进行中', className: 'bg-green-100 text-green-700' };
    case 'Paused':
      return { icon: PauseCircle, label: '暂停', className: 'bg-yellow-100 text-yellow-700' };
    case 'Completed':
      return { icon: CheckCircle2, label: '已完成', className: 'bg-emerald-100 text-emerald-700' };
    case 'Cancelled':
      return { icon: XCircle, label: '已取消', className: 'bg-red-100 text-red-700' };
    default:
      return { icon: AlertCircle, label: '未知', className: 'bg-gray-100 text-gray-700' };
  }
};

/**
 * 获取卡片尺寸样式
 */
const getSizeStyles = (size: CardConfig['size']) => {
  switch (size) {
    case 'small':
      return 'min-h-[200px] max-w-[280px]';
    case 'medium':
      return 'min-h-[280px] max-w-[320px]';
    case 'large':
      return 'min-h-[360px] max-w-[380px]';
    case 'extra-large':
      return 'min-h-[440px] max-w-[420px]';
    default:
      return 'min-h-[280px] max-w-[320px]';
  }
};

/**
 * 获取主题样式
 */
const getThemeStyles = (theme: CardConfig['theme']) => {
  switch (theme) {
    case 'modern':
      return 'bg-gradient-to-br from-slate-50 to-slate-100 border-slate-200';
    case 'glass':
      return 'bg-white/80 backdrop-blur-sm border-white/20';
    case 'gradient':
      return 'bg-gradient-to-br from-blue-50 via-white to-purple-50 border-blue-200';
    case 'dark':
      return 'bg-slate-800 border-slate-700 text-white';
    default:
      return 'bg-white border-gray-200';
  }
};

/**
 * 获取间距样式
 */
const getSpacingStyles = (spacing: CardConfig['spacing']) => {
  switch (spacing) {
    case 'tight':
      return 'p-3 space-y-2';
    case 'loose':
      return 'p-6 space-y-4';
    default:
      return 'p-4 space-y-3';
  }
};

/**
 * 获取圆角样式
 */
const getBorderRadiusStyles = (borderRadius: CardConfig['borderRadius']) => {
  switch (borderRadius) {
    case 'none':
      return 'rounded-none';
    case 'small':
      return 'rounded-sm';
    case 'medium':
      return 'rounded-md';
    case 'large':
      return 'rounded-lg';
    case 'full':
      return 'rounded-2xl';
    default:
      return 'rounded-md';
  }
};

/**
 * 获取阴影样式
 */
const getShadowStyles = (shadow: CardConfig['shadow']) => {
  switch (shadow) {
    case 'none':
      return 'shadow-none';
    case 'small':
      return 'shadow-sm';
    case 'medium':
      return 'shadow-md';
    case 'large':
      return 'shadow-lg';
    case 'glow':
      return 'shadow-lg shadow-blue-500/25';
    default:
      return 'shadow-sm';
  }
};

/**
 * 获取动画样式
 */
const getAnimationStyles = (animation: CardConfig['animation']) => {
  switch (animation) {
    case 'subtle':
      return 'transition-all duration-200 hover:shadow-md';
    case 'smooth':
      return 'transition-all duration-300 hover:scale-[1.02] hover:shadow-lg';
    case 'bouncy':
      return 'transition-all duration-300 hover:scale-105 hover:shadow-xl hover:rotate-1';
    default:
      return '';
  }
};

/**
 * 卡片配置预览组件
 * 用于实时预览卡片配置效果
 */
export const CardConfigPreview: React.FC<CardConfigPreviewProps> = ({
  config,
  className
}) => {
  const statusInfo = getStatusInfo(mockTask.dispatchStatus);
  const StatusIcon = statusInfo.icon;

  const cardClasses = cn(
    'relative overflow-hidden transition-all duration-200',
    getSizeStyles(config.size),
    getThemeStyles(config.theme),
    getSpacingStyles(config.spacing),
    getBorderRadiusStyles(config.borderRadius),
    getShadowStyles(config.shadow),
    getAnimationStyles(config.animation),
    className
  );

  const headerHeight = config.content.header.height === 'compact' ? 'h-12' :
    config.content.header.height === 'expanded' ? 'h-20' : 'h-16';

  const footerHeight = config.content.footer.height === 'compact' ? 'h-12' :
    config.content.footer.height === 'expanded' ? 'h-20' : 'h-16';

  return (
    <Card className={cardClasses}>
      <CardContent className="p-0 h-full flex flex-col">
        {/* 头部区域 */}
        {config.content.header.show && (
          <div className={cn('flex items-center justify-between px-4 py-2 border-b', headerHeight)}>
            <div className="flex items-center gap-2 min-w-0 flex-1">
              <div className="flex flex-col min-w-0">
                <div className="font-medium text-sm truncate">{mockTask.taskNumber}</div>
                <div className="text-xs text-muted-foreground truncate">{mockTask.customerName}</div>
              </div>
            </div>
            <div className="flex items-center gap-2 flex-shrink-0">
              {config.content.header.showStatus && (
                <Badge className={cn('text-xs', statusInfo.className)}>
                  <StatusIcon className="w-3 h-3 mr-1" />
                  {statusInfo.label}
                </Badge>
              )}
              {config.content.header.showProgress && (
                <div className="text-xs font-medium">{mockTask.progress}%</div>
              )}
              {config.content.header.showReminder && mockTask.hasReminder && (
                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
              )}
            </div>
          </div>
        )}

        {/* 主体区域 */}
        {config.content.body.show && (
          <div className="flex-1 p-4">
            {config.content.body.layout === 'info-only' && (
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <Building2 className="w-4 h-4 text-muted-foreground" />
                  <span className="truncate">{mockTask.projectName}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <MapPin className="w-4 h-4 text-muted-foreground" />
                  <span className="truncate">{mockTask.location}</span>
                </div>
              </div>
            )}

            {config.content.body.layout === 'progress-only' && (
              <div className="flex items-center justify-center h-full">
                <div className="relative w-24 h-24">
                  <div className="absolute inset-0 rounded-full border-4 border-gray-200" />
                  <div 
                    className="absolute inset-0 rounded-full border-4 border-blue-500 border-t-transparent animate-spin"
                    style={{
                      transform: `rotate(${(mockTask.progress / 100) * 360}deg)`
                    }}
                  />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-lg font-bold">{mockTask.progress}%</span>
                  </div>
                </div>
              </div>
            )}

            {config.content.body.layout === 'info-progress' && (
              <div className="flex items-center gap-4">
                <div className="flex-1 space-y-2">
                  <div className="flex items-center gap-2 text-sm">
                    <Building2 className="w-4 h-4 text-muted-foreground" />
                    <span className="truncate">{mockTask.projectName}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <MapPin className="w-4 h-4 text-muted-foreground" />
                    <span className="truncate">{mockTask.location}</span>
                  </div>
                </div>
                {config.content.body.showProgressRing && (
                  <div className="relative w-16 h-16 flex-shrink-0">
                    <div className="absolute inset-0 rounded-full border-4 border-gray-200" />
                    <div 
                      className="absolute inset-0 rounded-full border-4 border-blue-500 border-t-transparent"
                      style={{
                        transform: `rotate(${(mockTask.progress / 100) * 360}deg)`
                      }}
                    />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-sm font-bold">{mockTask.progress}%</span>
                    </div>
                  </div>
                )}
              </div>
            )}

            {config.content.body.layout === 'custom' && (
              <div className="space-y-3">
                {config.content.body.showTaskInfo && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm">
                      <Building2 className="w-4 h-4 text-muted-foreground" />
                      <span className="truncate">{mockTask.projectName}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <MapPin className="w-4 h-4 text-muted-foreground" />
                      <span className="truncate">{mockTask.location}</span>
                    </div>
                  </div>
                )}
                {config.content.body.showProgressRing && (
                  <div className={cn(
                    'flex',
                    config.content.body.progressPosition === 'center' && 'justify-center',
                    config.content.body.progressPosition === 'left' && 'justify-start',
                    config.content.body.progressPosition === 'right' && 'justify-end'
                  )}>
                    <div className="relative w-16 h-16">
                      <div className="absolute inset-0 rounded-full border-4 border-gray-200" />
                      <div 
                        className="absolute inset-0 rounded-full border-4 border-blue-500 border-t-transparent"
                        style={{
                          transform: `rotate(${(mockTask.progress / 100) * 360}deg)`
                        }}
                      />
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-sm font-bold">{mockTask.progress}%</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* 底部区域 */}
        {config.content.footer.show && (
          <div className={cn('border-t px-4 py-2', footerHeight)}>
            {config.content.footer.showVehicleList && (
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Truck className="w-3 h-3" />
                  <span>派车情况 ({mockTask.vehicles.length})</span>
                </div>
                <div className={cn(
                  'flex gap-1',
                  config.content.footer.vehicleDisplayMode === 'grid' && 'flex-wrap',
                  config.content.footer.vehicleDisplayMode === 'list' && 'flex-col space-y-1',
                  config.content.footer.vehicleDisplayMode === 'compact' && 'flex-row'
                )}>
                  {mockTask.vehicles.slice(0, config.content.footer.vehicleDisplayMode === 'compact' ? 2 : 3).map((vehicle, index) => (
                    <Badge
                      key={vehicle.id}
                      variant="outline"
                      className={cn(
                        'text-xs',
                        config.content.footer.vehicleDisplayMode === 'compact' && 'px-1 py-0.5',
                        vehicle.status === 'dispatched' ? 'bg-green-50 text-green-700 border-green-200' : 'bg-gray-50 text-gray-700 border-gray-200'
                      )}
                    >
                      {vehicle.plateNumber}
                    </Badge>
                  ))}
                  {mockTask.vehicles.length > (config.content.footer.vehicleDisplayMode === 'compact' ? 2 : 3) && (
                    <Badge variant="outline" className="text-xs bg-gray-50 text-gray-500">
                      +{mockTask.vehicles.length - (config.content.footer.vehicleDisplayMode === 'compact' ? 2 : 3)}
                    </Badge>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {/* 右侧隐藏面板指示器 */}
        {config.content.sidePanel.enabled && (
          <div 
            className={cn(
              'absolute right-0 top-0 bottom-0 bg-blue-500/20 border-l border-blue-300',
              config.content.sidePanel.width === 'narrow' && 'w-1',
              config.content.sidePanel.width === 'normal' && 'w-2',
              config.content.sidePanel.width === 'wide' && 'w-3'
            )}
            style={{ opacity: config.content.sidePanel.opacity }}
          >
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <div className="w-1 h-4 bg-blue-500 rounded-full" />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};