// src/components/sections/task-list/cards/ProductionLinePanel.tsx
'use client';

import React, { useMemo } from 'react';
import { useDrop, DropTargetMonitor } from 'react-dnd';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { X, Factory, TruckIcon, ArrowRight } from 'lucide-react';
import type { Task, Vehicle } from '@/types';
import { ItemTypes } from '@/constants/dndItemTypes';

interface DraggableVehicleItem {
  vehicle: Vehicle;
  index: number;
  statusList: 'pending' | 'returned';
  type: typeof ItemTypes.VEHICLE_CARD_DISPATCH;
}

interface ProductionLinePanelProps {
  isVisible: boolean;
  task: Task;
  productionLineCount: number;
  onClosePanel: () => void;
  onDropVehicleOnLine: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  activeDragData?: { vehicleNumber?: string; type?: string; vehicle?: Vehicle } | null; // Added vehicle to activeDragData
}

interface ProductionLineSlotProps {
  lineId: string;
  lineNumber: number;
  task: Task;
  onDropVehicleOnLine: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  activeDragData?: { vehicleNumber?: string; type?: string; vehicle?: Vehicle } | null;
}

const ProductionLineSlot: React.FC<ProductionLineSlotProps> = React.memo(({
  lineId,
  lineNumber,
  task,
  onDropVehicleOnLine,
  activeDragData
}) => {
  const [{ canDrop, isOver }, drop] = useDrop({
    accept: ItemTypes.VEHICLE_CARD_DISPATCH,
    canDrop: (item: DraggableVehicleItem, monitor) => {
      return task.dispatchStatus === 'InProgress';
    },
    drop: (item: DraggableVehicleItem, monitor) => {
      if (monitor.canDrop()) {
        onDropVehicleOnLine(item.vehicle, task.id, lineId);
      }
    },
    collect: (monitor: DropTargetMonitor<DraggableVehicleItem, void>) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  });

  const slotStyles = useMemo(() => {
    const baseStyles = cn(
      "relative border-2 border-dashed rounded-lg p-4 transition-all duration-300",
      "flex flex-col items-center justify-center gap-2 min-h-[120px]",
      task.dispatchStatus !== 'InProgress' ? "opacity-50 cursor-not-allowed" : "hover:border-primary/50 hover:bg-primary/5"
    );

    if (isOver && canDrop && activeDragData?.type === ItemTypes.VEHICLE_CARD_DISPATCH) {
      return cn(baseStyles,
        "border-primary bg-primary/10 shadow-lg scale-105",
        "ring-2 ring-primary/30"
      );
    }
    return cn(baseStyles, "border-muted-foreground/30 bg-muted/20");
  }, [isOver, canDrop, task.dispatchStatus, activeDragData]);

  return (
    <div ref={(node) => drop(node)} className={slotStyles}>
      <div className="flex flex-col items-center gap-2">
        <div className={cn(
          "w-12 h-12 rounded-full flex items-cente r justify-center",
          "bg-gradient-to-br from-blue-500 to-blue-600 text-white shadow-lg"
        )}>
          <Factory className="w-6 h-6" />
        </div>
        <div className="text-center">
          <div className="font-semibold text-sm">生产线 {lineNumber}</div>
          <div className="text-xs text-muted-foreground">
            {task.constructionSite}
          </div>
        </div>
      </div>
      {activeDragData?.type === ItemTypes.VEHICLE_CARD_DISPATCH && task.dispatchStatus === 'InProgress' && (
        <div className="flex items-center gap-2 text-xs text-muted-foreground mt-2">
          <TruckIcon className="w-4 h-4" />
          <ArrowRight className="w-3 h-3" />
          <span>拖拽车辆到此发车</span>
        </div>
      )}
      {isOver && canDrop && activeDragData?.type === ItemTypes.VEHICLE_CARD_DISPATCH && task.dispatchStatus === 'InProgress' && (
        <div className="absolute top-2 right-2">
          <div className="w-3 h-3 bg-primary rounded-full animate-ping" />
          <div className="absolute top-0 right-0 w-3 h-3 bg-primary rounded-full" />
        </div>
      )}
    </div>
  );
});
ProductionLineSlot.displayName = 'ProductionLineSlot';

export const ProductionLinePanel: React.FC<ProductionLinePanelProps> = React.memo(({
  isVisible,
  task,
  productionLineCount,
  onClosePanel,
  onDropVehicleOnLine,
  activeDragData
}) => {
  const productionLines = useMemo(() => {
    return Array.from({ length: productionLineCount }, (_, index) => ({
      id: `L${index + 1}`,
      number: index + 1,
    }));
  }, [productionLineCount]);

  if (!isVisible) return null;

  return (
    <>
      <div
        className={cn(
          "fixed inset-0 bg-black/20 backdrop-blur-sm z-40 transition-opacity duration-300",
          isVisible ? "opacity-100" : "opacity-0 pointer-events-none"
        )}
        onClick={onClosePanel}
      />
      <div
        className={cn(
          "absolute top-0 right-0 h-full w-80 bg-background/95 backdrop-blur-md",
          "border-l border-border shadow-2xl z-[990]",
          "flex flex-col transition-transform duration-300",
          isVisible ? "translate-x-0" : "translate-x-full"
        )}
      >
        <div className="flex items-center justify-between p-4 border-b border-border">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
              <Factory className="w-4 h-4 text-primary" />
            </div>
            <div>
              <h3 className="font-semibold text-sm">选择生产线</h3>
              <p className="text-xs text-muted-foreground truncate max-w-[150px]">
                {task.taskNumber} - {task.constructionSite}
              </p>
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={onClosePanel} className="h-8 w-8 p-0">
            <X className="w-4 h-4" />
          </Button>
        </div>

        {activeDragData?.type === ItemTypes.VEHICLE_CARD_DISPATCH && activeDragData.vehicle && (
          <div className="p-4 bg-primary/5 border-b border-primary/20">
            <div className="flex items-center gap-2 text-sm text-primary">
              <TruckIcon className="w-4 h-4" />
              <span>拖拽车辆：{activeDragData.vehicle.vehicleNumber || '未知车辆'}</span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              将车辆拖拽到下方生产线方格完成发车。
            </p>
          </div>
        )}

        <div className="flex-1 p-4 overflow-y-auto space-y-4 custom-thin-scrollbar">
          {productionLines.map((line) => (
            <ProductionLineSlot
              key={line.id}
              lineId={line.id}
              lineNumber={line.number}
              task={task}
              onDropVehicleOnLine={onDropVehicleOnLine}
              activeDragData={activeDragData}
            />
          ))}
        </div>
        <div className="p-4 border-t border-border bg-muted/30 text-xs text-muted-foreground">
          共 {productionLineCount} 条生产线可用。
        </div>
      </div>
    </>
  );
});
ProductionLinePanel.displayName = 'ProductionLinePanel';
