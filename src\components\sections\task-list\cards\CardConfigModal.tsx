import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Settings, Palette, FileText, RotateCcw } from 'lucide-react';
import { CardConfigPreview } from './CardConfigPreview';
import { CardStyleConfigModal, CardStyleConfig } from './CardStyleConfigModal';
import { CardContentConfigModal, CardContentConfig } from './CardContentConfigModal';

export interface CardConfig {
  style: CardStyleConfig;
  content: CardContentConfig;
}

interface CardConfigModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  config: CardConfig;
  onConfigChange?: (config: CardConfig) => void;
}

export const CardConfigModal: React.FC<CardConfigModalProps> = ({
  open,
  onOpenChange,
  config,
  onConfigChange
}) => {
  const [previewConfig, setPreviewConfig] = useState<CardConfig>(config);
  const [styleModalOpen, setStyleModalOpen] = useState(false);
  const [contentModalOpen, setContentModalOpen] = useState(false);

  // 同步外部配置到预览配置
  React.useEffect(() => {
    setPreviewConfig(config);
  }, [config]);

  const updateStyleConfig = (styleConfig: CardStyleConfig) => {
    const newConfig = {
      ...previewConfig,
      style: styleConfig
    };
    setPreviewConfig(newConfig);
  };

  const updateContentConfig = (contentConfig: CardContentConfig) => {
    const newConfig = {
      ...previewConfig,
      content: contentConfig
    };
    setPreviewConfig(newConfig);
  };

  const applyConfig = () => {
    onConfigChange?.(previewConfig);
  };

  const resetConfig = () => {
    setPreviewConfig(config);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            卡片配置
          </DialogTitle>
        </DialogHeader>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 配置区域 */}
          <div className="space-y-6">
            {/* 样式配置 */}
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <Palette className="w-5 h-5 text-blue-500" />
                    <h3 className="font-medium">样式配置</h3>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setStyleModalOpen(true)}
                  >
                    配置样式
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground">
                  配置卡片的主题、尺寸、布局、间距等样式属性
                </p>
              </CardContent>
            </Card>

            {/* 内容配置 */}
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <FileText className="w-5 h-5 text-green-500" />
                    <h3 className="font-medium">内容配置</h3>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setContentModalOpen(true)}
                  >
                    配置内容
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground">
                  配置卡片各区域的显示内容、字段样式等
                </p>
              </CardContent>
            </Card>
          </div>

          {/* 实时预览 */}
          <div className="space-y-4">
            <h3 className="font-medium flex items-center gap-2">
              <Settings className="w-4 h-4" />
              实时预览
            </h3>
            <div className="border rounded-lg p-4 bg-muted/30">
              <CardConfigPreview config={previewConfig} />
            </div>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="flex justify-between pt-4 border-t">
          <Button
            variant="outline"
            onClick={resetConfig}
            className="flex items-center gap-2"
          >
            <RotateCcw className="w-4 h-4" />
            重置
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button onClick={() => {
              applyConfig();
              onOpenChange(false);
            }}>
              应用配置
            </Button>
          </div>
        </div>
      </DialogContent>

      {/* 样式配置子模态框 */}
      <CardStyleConfigModal
        open={styleModalOpen}
        onOpenChange={setStyleModalOpen}
        config={previewConfig.style}
        onConfigChange={updateStyleConfig}
      />

      {/* 内容配置子模态框 */}
      <CardContentConfigModal
        open={contentModalOpen}
        onOpenChange={setContentModalOpen}
        config={previewConfig.content}
        onConfigChange={updateContentConfig}
      />
    </Dialog>
  );
};

export default CardConfigModal;
