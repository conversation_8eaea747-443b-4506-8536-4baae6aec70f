'use client';

import React, { useState } from 'react';
import { MessageCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { useReminderStore } from '@/store/reminderStore';
import type { Task, ReminderMessage } from '@/types';

interface MessageCellProps {
  task: Task;
  textClassName?: string;
}

interface MessageItemProps {
  message: ReminderMessage;
  onMarkAsRead: (id: string) => void;
}

const MessageItem: React.FC<MessageItemProps> = ({ message, onMarkAsRead }) => {
  // 格式化时间为"今天 HH:mm"或"MM-DD HH:mm"
  const formatMessageTime = (timestamp: number): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const isToday = date.getDate() === now.getDate() && 
                    date.getMonth() === now.getMonth() && 
                    date.getFullYear() === now.getFullYear();
    
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    
    if (isToday) {
      return `今天 ${hours}:${minutes}`;
    } else {
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${month}-${day} ${hours}:${minutes}`;
    }
  };

  return (
    <div className={cn(
      "px-4 py-3 border-b last:border-0",
      !message.read && "bg-primary/5"
    )}>
      <div className="flex justify-between items-start">
        <div className="flex items-start gap-2">
          <div className="mt-0.5">
            <MessageCircle className="h-4 w-4 text-primary" />
          </div>
          <div>
            <div className="flex items-center gap-2">
              <span className="font-medium text-sm">{message.title}</span>
              {!message.read && (
                <Badge variant="secondary" className="px-1.5 h-5 bg-primary/20 text-primary text-[10px]">
                  新消息
                </Badge>
              )}
            </div>
            <p className="text-muted-foreground text-xs mt-1">{message.description}</p>
            <div className="flex items-center gap-2 mt-2 text-[10px] text-muted-foreground">
              <div className="flex items-center">
                <span className="mr-1">{message.taskNumber}</span>
              </div>
              <div className="flex items-center">
                <span>{formatMessageTime(message.time)}</span>
              </div>
            </div>
          </div>
        </div>
        {!message.read && (
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-6 w-6 text-primary" 
            onClick={() => onMarkAsRead(message.id)}
          >
            <span className="sr-only">标记为已读</span>
            ✓
          </Button>
        )}
      </div>
    </div>
  );
};

export const MessageCell: React.FC<MessageCellProps> = ({ task }) => {
  const [open, setOpen] = useState(false);
  const { messages, markAsRead, markAllAsRead } = useReminderStore();
  
  // 过滤出当前任务的消息
  const taskMessages = messages.filter(msg => msg.taskId === task.id);
  
  // 未读消息数量
  const unreadCount = taskMessages.filter(msg => !msg.read).length;

  // 按照未读状态和时间排序消息
  const sortedMessages = [...taskMessages].sort((a, b) => {
    // 首先按照未读状态排序
    if (a.read !== b.read) {
      return a.read ? 1 : -1; // 未读消息排在前面
    }
    // 然后按照时间倒序排序
    return b.time - a.time;
  });

  // 标记当前任务的所有消息为已读
  const markAllTaskMessagesAsRead = () => {
    taskMessages.forEach(msg => {
      if (!msg.read) {
        markAsRead(msg.id);
      }
    });
  };

  return (
    <div className="flex items-center justify-center h-full relative after:absolute after:right-0 after:top-0 after:h-full after:w-[1px] after:shadow-[2px_0_4px_rgba(0,0,0,0.1)] after:content-['']">
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="relative cursor-pointer p-1">
                  <MessageCircle className="h-5 w-5 text-primary" />
                  {unreadCount > 0 && (
                    <Badge 
                      className="absolute -top-1 -right-1 px-1 min-w-[16px] h-4 text-[10px] flex items-center justify-center bg-destructive text-destructive-foreground"
                      variant="destructive"
                    >
                      {unreadCount > 9 ? '9+' : unreadCount}
                    </Badge>
                  )}
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>任务消息</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </DialogTrigger>
        <DialogContent className="sm:max-w-md">
          <DialogHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <DialogTitle>调度消息中心</DialogTitle>
            {unreadCount > 0 && (
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-7 text-xs" 
                onClick={markAllTaskMessagesAsRead}
              >
                全部标为已读
              </Button>
            )}
          </DialogHeader>
          <ScrollArea className="max-h-[60vh] -mx-6">
            {sortedMessages.length > 0 ? (
              sortedMessages.map(message => (
                <MessageItem 
                  key={message.id} 
                  message={message} 
                  onMarkAsRead={markAsRead} 
                />
              ))
            ) : (
              <div className="py-8 text-center text-muted-foreground">
                暂无消息
              </div>
            )}
          </ScrollArea>
        </DialogContent>
      </Dialog>
    </div>
  );
};