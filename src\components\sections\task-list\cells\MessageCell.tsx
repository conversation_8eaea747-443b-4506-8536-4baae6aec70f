'use client';

import React, { useState } from 'react';
import { MessageCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { useReminderStore } from '@/store/reminderStore';
import type { Task, ReminderMessage } from '@/types';

interface MessageCellProps {
  task: Task;
  textClassName?: string;
}

interface MessageItemProps {
  message: ReminderMessage;
  onMarkAsRead: (id: string) => void;
}

const MessageItem: React.FC<MessageItemProps> = ({ message, onMarkAsRead }) => {
  // 格式化时间为"今天 HH:mm"或"MM-DD HH:mm"
  const formatMessageTime = (timestamp: number): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const isToday = date.getDate() === now.getDate() &&
                    date.getMonth() === now.getMonth() &&
                    date.getFullYear() === now.getFullYear();

    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    if (isToday) {
      return `今天 ${hours}:${minutes}`;
    } else {
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${month}-${day} ${hours}:${minutes}`;
    }
  };

  // 获取消息类型样式
  const getMessageTypeStyle = (type: string) => {
    switch (type) {
      case 'urgent':
        return 'border-l-4 border-red-500 bg-red-50 dark:bg-red-950/20';
      case 'highlight':
        return 'border-l-4 border-yellow-500 bg-yellow-50 dark:bg-yellow-950/20';
      case 'popup':
        return 'border-l-4 border-blue-500 bg-blue-50 dark:bg-blue-950/20';
      case 'error':
        return 'border-l-4 border-red-600 bg-red-100 dark:bg-red-950/30';
      default:
        return 'border-l-4 border-gray-300 bg-gray-50 dark:bg-gray-950/20';
    }
  };

  return (
    <div
      className={cn(
        "p-4 rounded-lg transition-all duration-200 cursor-pointer hover:shadow-md",
        getMessageTypeStyle(message.type || 'popup'),
        !message.read && "ring-2 ring-blue-200"
      )}
      onClick={() => !message.read && onMarkAsRead(message.id)}
    >
      <div className="flex items-start gap-3">
        <MessageCircle className="w-5 h-5 mt-0.5 flex-shrink-0 text-primary" />
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <span className="font-medium text-sm">{message.title}</span>
            {!message.read && (
              <Badge variant="secondary" className="px-1.5 h-5 bg-blue-100 text-blue-700 text-[10px]">
                新消息
              </Badge>
            )}
          </div>
          <p className="text-muted-foreground text-sm mb-2">{message.description}</p>
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>{message.taskNumber}</span>
            <span>{formatMessageTime(message.time)}</span>
          </div>
        </div>
        {!message.read && (
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 text-blue-600 hover:text-blue-800 flex-shrink-0"
            onClick={(e) => {
              e.stopPropagation();
              onMarkAsRead(message.id);
            }}
          >
            <span className="sr-only">标记为已读</span>
            ✓
          </Button>
        )}
      </div>
    </div>
  );
};

export const MessageCell: React.FC<MessageCellProps> = ({ task }) => {
  const [open, setOpen] = useState(false);
  const { messages, markAsRead, markAllAsRead } = useReminderStore();
  
  // 过滤出当前任务的消息
  const taskMessages = messages.filter(msg => msg.taskId === task.id);
  
  // 未读消息数量
  const unreadCount = taskMessages.filter(msg => !msg.read).length;

  // 按照未读状态和时间排序消息
  const sortedMessages = [...taskMessages].sort((a, b) => {
    // 首先按照未读状态排序
    if (a.read !== b.read) {
      return a.read ? 1 : -1; // 未读消息排在前面
    }
    // 然后按照时间倒序排序
    return b.time - a.time;
  });

  // 标记当前任务的所有消息为已读
  const markAllTaskMessagesAsRead = () => {
    taskMessages.forEach(msg => {
      if (!msg.read) {
        markAsRead(msg.id);
      }
    });
  };

  return (
    <div className="flex items-center justify-center h-full relative after:absolute after:right-0 after:top-0 after:h-full after:w-[1px] after:shadow-[2px_0_4px_rgba(0,0,0,0.1)] after:content-['']">
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="relative cursor-pointer p-1 hover:bg-gray-100 rounded transition-colors">
                  <MessageCircle className={cn(
                    "h-5 w-5",
                    unreadCount > 0 ? "text-blue-600" : "text-muted-foreground"
                  )} />
                  {/* 消息角标 - 优化显示样式，确保完整显示 */}
                  {unreadCount > 0 && (
                    <div className="absolute -top-1 -right-1 bg-red-500 text-white text-[10px] font-medium rounded-full min-w-[16px] h-4 flex items-center justify-center px-1 z-10">
                      {unreadCount > 9 ? '9+' : unreadCount}
                    </div>
                  )}
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>{unreadCount > 0 ? `${unreadCount} 条未读消息` : '查看消息'}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </DialogTrigger>
        <DialogContent className="max-w-2xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <MessageCircle className="w-5 h-5 text-blue-600" />
              <span>任务消息</span>
              <Badge variant="outline" className="ml-2">
                {task.taskNumber}
              </Badge>
              {unreadCount > 0 && (
                <Badge className="bg-red-500 text-white">
                  {unreadCount} 条未读
                </Badge>
              )}
            </DialogTitle>
            <div className="text-sm text-muted-foreground">
              {task.projectName} - {task.constructionSite}
            </div>
          </DialogHeader>

          <div className="space-y-4">
            {/* 操作按钮 */}
            {unreadCount > 0 && (
              <div className="flex justify-end">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 text-xs text-blue-600 hover:text-blue-800"
                  onClick={markAllTaskMessagesAsRead}
                >
                  全部标记为已读
                </Button>
              </div>
            )}

            {/* 消息列表 */}
            <ScrollArea className="h-[400px] pr-4">
              {sortedMessages.length > 0 ? (
                <div className="space-y-3">
                  {sortedMessages.map(message => (
                    <MessageItem
                      key={message.id}
                      message={message}
                      onMarkAsRead={markAsRead}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <MessageCircle className="w-12 h-12 mx-auto mb-4 text-muted-foreground/50" />
                  <p>暂无消息</p>
                </div>
              )}
            </ScrollArea>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};