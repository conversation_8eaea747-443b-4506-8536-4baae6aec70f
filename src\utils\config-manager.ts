/**
 * 配置管理工具类
 * 负责加载、保存和管理UI配置
 */

import type { TaskListStoredSettings, TaskCardConfig } from '@/types';
import { defaultTaskCardConfig } from '@/types/taskCardConfig';

// 配置版本和键名
export const CONFIG_VERSION = '1.0.0';
export const TASK_LIST_SETTINGS_KEY = 'taskListSettings_v4.0';
export const TASK_CARD_CONFIG_KEY = 'taskCardConfig_v2.0';
export const CONFIG_VERSION_KEY = 'uiConfigVersion';

// 默认配置接口
export interface DefaultUIConfig {
  version: string;
  lastUpdated: string;
  description: string;
  taskListSettings: TaskListStoredSettings;
  taskCardConfig: TaskCardConfig;
  cardViewConfig: {
    size: 'small' | 'medium' | 'large';
    layout: 'compact' | 'standard' | 'detailed' | 'minimal';
    theme: 'default' | 'modern' | 'glass' | 'gradient' | 'dark';
    spacing: 'tight' | 'normal' | 'loose';
    borderRadius: 'none' | 'small' | 'medium' | 'large' | 'full';
    shadow: 'none' | 'small' | 'medium' | 'large' | 'glow';
    animation: 'none' | 'subtle' | 'smooth' | 'bouncy';
    columns: 'auto' | '1' | '2' | '3' | '4' | '5' | '6';
  };
  performanceConfig: {
    virtualScroll: {
      enabled: boolean;
      overscan: number;
      increaseViewportBy: number;
      itemHeight: number;
    };
    animations: {
      enabled: boolean;
      duration: number;
      easing: string;
      reduceMotion: boolean;
    };
    rendering: {
      useMemo: boolean;
      useCallback: boolean;
      reactMemo: boolean;
      debounceMs: number;
    };
    scrolling: {
      smoothScrolling: boolean;
      scrollBehavior: string;
      throttleMs: number;
    };
  };
}

/**
 * 配置管理器类
 */
export class ConfigManager {
  private static instance: ConfigManager;
  private defaultConfig: DefaultUIConfig | null = null;

  private constructor() {}

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  /**
   * 加载默认配置
   */
  private async loadDefaultConfig(): Promise<DefaultUIConfig> {
    if (this.defaultConfig) {
      return this.defaultConfig;
    }

    try {
      const response = await fetch('/config/default-ui-config.json');
      if (!response.ok) {
        throw new Error(`Failed to load default config: ${response.statusText}`);
      }
      this.defaultConfig = await response.json();
      return this.defaultConfig;
    } catch (error) {
      console.error('Failed to load default UI config:', error);
      // 返回硬编码的默认配置作为后备
      return this.getFallbackConfig();
    }
  }

  /**
   * 获取后备配置（硬编码）
   */
  private getFallbackConfig(): DefaultUIConfig {
    return {
      version: CONFIG_VERSION,
      lastUpdated: new Date().toISOString(),
      description: 'Fallback UI configuration',
      taskListSettings: {
        displayMode: 'table',
        density: 'normal',
        enableZebraStriping: false,
        columnOrder: [],
        columnVisibility: {},
        columnWidths: {},
        columnTextStyles: {},
        columnBackgrounds: {},
        inTaskVehicleCardStyles: {
          cardWidth: 'w-14',
          cardHeight: 'h-8',
          fontSize: 'text-[10px]',
          fontColor: 'text-foreground',
          vehicleNumberFontWeight: 'font-medium',
          cardBgColor: 'bg-card/80',
          statusDotSize: 'w-1 h-1',
          borderRadius: 'rounded-md',
          boxShadow: 'shadow-sm',
          vehiclesPerRow: 4,
        },
        selectedPlantId: null,
        groupConfig: {
          groupBy: 'none',
          enabled: false,
          collapsible: true,
          defaultCollapsed: [],
          sortOrder: 'asc',
          showGroupStats: true,
          allowedGroupColumns: [],
          disallowedGroupColumns: [],
          groupHeaderStyle: {
            backgroundColor: 'bg-muted/50',
            textColor: 'text-foreground',
            fontSize: 'text-sm',
            fontWeight: 'font-medium',
            padding: 'py-2',
          },
        },
      },
      taskCardConfig: defaultTaskCardConfig,
      cardViewConfig: {
        size: 'small',
        layout: 'standard',
        theme: 'default',
        spacing: 'normal',
        borderRadius: 'medium',
        shadow: 'medium',
        animation: 'smooth',
        columns: 'auto',
      },
      performanceConfig: {
        virtualScroll: {
          enabled: true,
          overscan: 1,
          increaseViewportBy: 100,
          itemHeight: 300,
        },
        animations: {
          enabled: true,
          duration: 250,
          easing: 'ease-out',
          reduceMotion: false,
        },
        rendering: {
          useMemo: true,
          useCallback: true,
          reactMemo: true,
          debounceMs: 100,
        },
        scrolling: {
          smoothScrolling: true,
          scrollBehavior: 'smooth',
          throttleMs: 16,
        },
      },
    };
  }

  /**
   * 检查配置版本是否匹配
   */
  private isConfigVersionValid(): boolean {
    try {
      const storedVersion = localStorage.getItem(CONFIG_VERSION_KEY);
      return storedVersion === CONFIG_VERSION;
    } catch {
      return false;
    }
  }

  /**
   * 检查是否需要配置升级
   */
  private needsConfigUpgrade(): { needsUpgrade: boolean; fromVersion?: string; toVersion: string } {
    try {
      const storedVersion = localStorage.getItem(CONFIG_VERSION_KEY);
      if (!storedVersion) {
        return { needsUpgrade: true, toVersion: CONFIG_VERSION };
      }

      const needsUpgrade = storedVersion !== CONFIG_VERSION;
      return {
        needsUpgrade,
        fromVersion: storedVersion,
        toVersion: CONFIG_VERSION
      };
    } catch {
      return { needsUpgrade: true, toVersion: CONFIG_VERSION };
    }
  }

  /**
   * 执行配置升级
   */
  private async performConfigUpgrade(fromVersion?: string): Promise<void> {
    console.log(`Upgrading config from ${fromVersion || 'unknown'} to ${CONFIG_VERSION}`);

    // 这里可以添加具体的升级逻辑
    // 例如：迁移旧版本的配置格式、清理废弃的配置项等

    // 目前简单地清理所有旧配置，使用默认配置
    try {
      localStorage.removeItem(TASK_LIST_SETTINGS_KEY);
      localStorage.removeItem(TASK_CARD_CONFIG_KEY);
      this.setConfigVersion();
      console.log('Config upgrade completed');
    } catch (error) {
      console.error('Failed to perform config upgrade:', error);
      throw error;
    }
  }

  /**
   * 设置配置版本
   */
  private setConfigVersion(): void {
    try {
      localStorage.setItem(CONFIG_VERSION_KEY, CONFIG_VERSION);
    } catch (error) {
      console.warn('Failed to set config version:', error);
    }
  }

  /**
   * 加载任务列表设置
   */
  public async loadTaskListSettings(): Promise<TaskListStoredSettings> {
    // 检查是否需要升级
    const upgradeInfo = this.needsConfigUpgrade();
    if (upgradeInfo.needsUpgrade) {
      console.log('Config upgrade needed, performing upgrade...');
      await this.performConfigUpgrade(upgradeInfo.fromVersion);
      const defaultConfig = await this.loadDefaultConfig();
      return defaultConfig.taskListSettings;
    }

    // 尝试从 localStorage 读取
    try {
      const stored = localStorage.getItem(TASK_LIST_SETTINGS_KEY);
      if (stored) {
        const parsedConfig = JSON.parse(stored);
        console.log('Loaded task list settings from localStorage');
        return parsedConfig;
      }
    } catch (error) {
      console.warn('Failed to load task list settings from localStorage:', error);
    }

    // 回退到默认配置
    console.log('Loading default task list settings');
    const defaultConfig = await this.loadDefaultConfig();
    return defaultConfig.taskListSettings;
  }

  /**
   * 保存任务列表设置
   */
  public saveTaskListSettings(settings: TaskListStoredSettings): void {
    try {
      localStorage.setItem(TASK_LIST_SETTINGS_KEY, JSON.stringify(settings));
      this.setConfigVersion();
    } catch (error) {
      console.error('Failed to save task list settings:', error);
    }
  }

  /**
   * 加载任务卡片配置
   */
  public async loadTaskCardConfig(): Promise<TaskCardConfig> {
    // 检查是否需要升级
    const upgradeInfo = this.needsConfigUpgrade();
    if (upgradeInfo.needsUpgrade) {
      console.log('Config upgrade needed for card config, performing upgrade...');
      await this.performConfigUpgrade(upgradeInfo.fromVersion);
      const defaultConfig = await this.loadDefaultConfig();
      return defaultConfig.taskCardConfig;
    }

    // 尝试从 localStorage 读取
    try {
      const stored = localStorage.getItem(TASK_CARD_CONFIG_KEY);
      if (stored) {
        const parsedConfig = JSON.parse(stored);
        console.log('Loaded task card config from localStorage');
        return this.mergeTaskCardConfig(defaultTaskCardConfig, parsedConfig);
      }
    } catch (error) {
      console.warn('Failed to load task card config from localStorage:', error);
    }

    // 回退到默认配置
    console.log('Loading default task card config');
    const defaultConfig = await this.loadDefaultConfig();
    return defaultConfig.taskCardConfig;
  }

  /**
   * 保存任务卡片配置
   */
  public saveTaskCardConfig(config: TaskCardConfig): void {
    try {
      localStorage.setItem(TASK_CARD_CONFIG_KEY, JSON.stringify(config));
      this.setConfigVersion();
    } catch (error) {
      console.error('Failed to save task card config:', error);
    }
  }

  /**
   * 深度合并任务卡片配置
   */
  private mergeTaskCardConfig(defaultConfig: TaskCardConfig, savedConfig: any): TaskCardConfig {
    if (!savedConfig || typeof savedConfig !== 'object') {
      return defaultConfig;
    }

    return {
      style: { ...defaultConfig.style, ...savedConfig.style },
      areas: {
        top: {
          ...defaultConfig.areas.top,
          ...savedConfig.areas?.top,
          fields: {
            ...defaultConfig.areas.top.fields,
            ...savedConfig.areas?.top?.fields
          }
        },
        vehicle: {
          ...defaultConfig.areas.vehicle,
          ...savedConfig.areas?.vehicle,
          fields: {
            ...defaultConfig.areas.vehicle.fields,
            ...savedConfig.areas?.vehicle?.fields
          }
        },
        content: {
          ...defaultConfig.areas.content,
          ...savedConfig.areas?.content,
          fields: {
            ...defaultConfig.areas.content.fields,
            ...savedConfig.areas?.content?.fields
          }
        },
        bottom: {
          ...defaultConfig.areas.bottom,
          ...savedConfig.areas?.bottom,
          fields: {
            ...defaultConfig.areas.bottom.fields,
            ...savedConfig.areas?.bottom?.fields
          }
        }
      }
    };
  }

  /**
   * 重置所有配置
   */
  public async resetAllConfigs(): Promise<void> {
    try {
      localStorage.removeItem(TASK_LIST_SETTINGS_KEY);
      localStorage.removeItem(TASK_CARD_CONFIG_KEY);
      localStorage.removeItem(CONFIG_VERSION_KEY);
      this.defaultConfig = null; // 清除缓存，强制重新加载
      console.log('All configs reset successfully');
    } catch (error) {
      console.error('Failed to reset configs:', error);
    }
  }

  /**
   * 获取默认配置（用于重置）
   */
  public async getDefaultConfig(): Promise<DefaultUIConfig> {
    return await this.loadDefaultConfig();
  }

  /**
   * 验证配置完整性
   */
  public validateConfig(config: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 验证基本结构
    if (!config || typeof config !== 'object') {
      errors.push('配置不是有效的对象');
      return { isValid: false, errors };
    }

    // 验证版本
    if (!config.version || typeof config.version !== 'string') {
      errors.push('缺少有效的版本信息');
    }

    // 验证任务列表设置
    if (!config.taskListSettings || typeof config.taskListSettings !== 'object') {
      errors.push('缺少任务列表设置');
    }

    // 验证任务卡片配置
    if (!config.taskCardConfig || typeof config.taskCardConfig !== 'object') {
      errors.push('缺少任务卡片配置');
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * 获取配置信息
   */
  public getConfigInfo(): { version: string; hasLocalStorage: boolean; configKeys: string[] } {
    const configKeys: string[] = [];
    let hasLocalStorage = false;

    try {
      if (localStorage.getItem(TASK_LIST_SETTINGS_KEY)) {
        configKeys.push('taskListSettings');
      }
      if (localStorage.getItem(TASK_CARD_CONFIG_KEY)) {
        configKeys.push('taskCardConfig');
      }
      if (localStorage.getItem(CONFIG_VERSION_KEY)) {
        configKeys.push('version');
      }
      hasLocalStorage = configKeys.length > 0;
    } catch {
      // localStorage 不可用
    }

    return {
      version: CONFIG_VERSION,
      hasLocalStorage,
      configKeys
    };
  }
}

// 导出单例实例
export const configManager = ConfigManager.getInstance();
