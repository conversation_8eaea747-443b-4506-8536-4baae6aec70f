// src/components/modals/vehicle-card-styler-modal.tsx
'use client';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import type { InTaskVehicleCardStyle, Vehicle, TaskListDensityMode } from '@/types';
import { cn, getProductionStatusColor, getProductionStatusTooltip } from '@/lib/utils'; // Updated import
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { PauseCircleIcon, XCircleIcon, Factory } from 'lucide-react';
import { VehicleCardBackgroundSettings, getVehicleCardBackgroundStyle } from './vehicle-card-background-settings';

interface VehicleCardStylerModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  currentStyles: InTaskVehicleCardStyle;
  onStylesChange: (newStyles: InTaskVehicleCardStyle) => void;
  onVehiclesPerRowChange?: (vehiclesPerRow: 2 | 3 | 4 | 5 | 6 | 7 | 8) => void;
}

const cardWidthOptions: { label: string; value: InTaskVehicleCardStyle['cardWidth'] }[] = [
  { label: '超窄 (32px)', value: 'w-8' },
  { label: '特窄 (36px)', value: 'w-9' },
  { label: '窄 (40px)', value: 'w-10' }, // Added w-10
  { label: '紧凑 (48px)', value: 'w-12' },
  { label: '标准-窄 (56px)', value: 'w-14' },
  { label: '标准 (64px)', value: 'w-16' },
  { label: '宽松 (80px)', value: 'w-20' },
  { label: '特宽 (96px)', value: 'w-24' },
];

const cardHeightOptions: { label: string; value: InTaskVehicleCardStyle['cardHeight'] }[] = [
  { label: '紧凑 (28px)', value: 'h-7' },
  { label: '标准 (32px)', value: 'h-8' },
  { label: '宽松 (36px)', value: 'h-9' },
  { label: '特高 (40px)', value: 'h-10' },
];

const vehiclesPerRowOptions: { label: string; value: NonNullable<InTaskVehicleCardStyle['vehiclesPerRow']> }[] = [
  { label: '2个车辆', value: 2 },
  { label: '3个车辆', value: 3 },
  { label: '4个车辆 (默认)', value: 4 },
  { label: '5个车辆', value: 5 },
  { label: '6个车辆', value: 6 },
  { label: '7个车辆', value: 7 },
  { label: '8个车辆', value: 8 },
];

const fontSizes: {label: string; value: InTaskVehicleCardStyle['fontSize']}[] = [
  {label: '特小 (8px)', value: 'text-[8px]'},
  {label: '小 (9px)', value: 'text-[9px]'},
  {label: '默认 (10px)', value: 'text-[10px]'},
  {label: '中 (11px)', value: 'text-[11px]'},
  {label: '大 (12px)', value: 'text-[12px]'},
];

const fontColors = [
  { label: '默认 (前景)', value: 'text-foreground' },
  { label: '主要 (主题蓝)', value: 'text-primary' },
  { label: '强调 (主题橙)', value: 'text-accent' },
  { label: '柔和 (灰色)', value: 'text-muted-foreground' },
  { label: '成功 (绿色)', value: 'text-green-600 dark:text-green-400' },
  { label: '警告 (黄色)', value: 'text-amber-600 dark:text-amber-400' },
  { label: '危险 (红色)', value: 'text-red-600 dark:text-red-400' },
  { label: '信息 (蓝色)', value: 'text-sky-600 dark:text-sky-400' },
  { label: '紫色', value: 'text-purple-600 dark:text-purple-400'},
  { label: '粉色', value: 'text-pink-600 dark:text-pink-400'},
];

const vehicleNumberFontWeightOptions: { label: string; value: InTaskVehicleCardStyle['vehicleNumberFontWeight'] }[] = [
  { label: '正常', value: 'font-normal' },
  { label: '中等', value: 'font-medium' },
  { label: '半粗', value: 'font-semibold' },
  { label: '加粗', value: 'font-bold' },
];




const statusDotSizeOptions: { label: string; value: InTaskVehicleCardStyle['statusDotSize'] }[] = [
  { label: '超小 (2px)', value: 'w-0.5 h-0.5' },
  { label: '小 (3px)', value: 'w-[3px] h-[3px]' },
  { label: '中 (4px)', value: 'w-1 h-1' },
  { label: '大 (5px)', value: 'w-[5px] h-[5px]' },
  { label: '特大 (6px)', value: 'w-1.5 h-1.5' },
];

const borderRadiusOptions: { label: string; value: InTaskVehicleCardStyle['borderRadius'] }[] = [
  { label: '无', value: 'rounded-none' },
  { label: '小', value: 'rounded-sm' },
  { label: '中', value: 'rounded-md' },
  { label: '大', value: 'rounded-lg' },
];

const boxShadowOptions: { label: string; value: InTaskVehicleCardStyle['boxShadow'] }[] = [
  { label: '无', value: 'shadow-none' },
  { label: '小', value: 'shadow-sm' },
  { label: '中', value: 'shadow-md' },
  { label: '大 (粉色系)', value: 'shadow-lg shadow-pink-500/30 dark:shadow-pink-700/30' },
  { label: '大 (红色系)', value: 'shadow-lg shadow-red-500/70 dark:shadow-red-400/60' },
  { label: '特大', value: 'shadow-xl' },
];

const getPreviewColorClass = (tailwindClass: string | undefined): string => {
  if (typeof tailwindClass !== 'string' || tailwindClass.trim() === '') {
    return 'bg-gray-500';
  }
  if (tailwindClass.includes('foreground')) return 'bg-foreground';
  if (tailwindClass.includes('primary')) return 'bg-primary';
  if (tailwindClass.includes('accent')) return 'bg-accent';
  if (tailwindClass.includes('secondary')) return 'bg-secondary';
  if (tailwindClass.includes('muted')) return 'bg-muted';
  if (tailwindClass.includes('border')) return 'bg-border';
  if (tailwindClass.startsWith('text-')) return `bg-${tailwindClass.substring(5)}`;
  if (tailwindClass.startsWith('bg-')) return tailwindClass;
  return 'bg-gray-500';
};

// 生成自定义渐变样式
const generateCustomGradient = (styles: InTaskVehicleCardStyle): { className: string; style?: React.CSSProperties } => {
  if (!styles.gradientEnabled || !styles.gradientDirection || !styles.gradientStartColor || !styles.gradientEndColor) {
    return { className: '' };
  }

  const direction = styles.gradientDirection;
  const startColor = styles.gradientStartColor;
  const endColor = styles.gradientEndColor;

  // 将Tailwind方向转换为CSS渐变方向
  const directionMap: Record<string, string> = {
    'to-r': 'to right',
    'to-l': 'to left',
    'to-t': 'to top',
    'to-b': 'to bottom',
    'to-tr': 'to top right',
    'to-tl': 'to top left',
    'to-br': 'to bottom right',
    'to-bl': 'to bottom left',
    'radial': 'radial-gradient'
  };

  const cssDirection = directionMap[direction] || 'to right';

  if (direction === 'radial') {
    return {
      className: '',
      style: {
        background: `radial-gradient(circle, ${startColor}, ${endColor})`
      }
    };
  } else {
    return {
      className: '',
      style: {
        background: `linear-gradient(${cssDirection}, ${startColor}, ${endColor})`
      }
    };
  }
};

/**
 * 预览角标指示器组件 - 优化版
 * 使用小型彩色圆点+微型图标的组合方式显示车辆状态
 * 更加节省空间且视觉清晰度高
 */
const PreviewCornerRibbonIndicator: React.FC<{
  vehicle: Partial<Vehicle>;
  isDispatchPanelView?: boolean;
}> = ({ vehicle, isDispatchPanelView }) => {
  // 定义角标状态
  let indicatorType: 'paused' | 'deactivated' | 'production-line' | null = null;
  let tooltipText = '';
  let productionLineId = '';

  // 确定角标类型
  if (vehicle.operationalStatus === 'paused') {
    indicatorType = 'paused';
    tooltipText = '车辆暂停出车';
  } else if (vehicle.operationalStatus === 'deactivated') {
    indicatorType = 'deactivated';
    tooltipText = '车辆已停用';
  } else if (vehicle.assignedProductionLineId && !isDispatchPanelView) {
    indicatorType = 'production-line';
    productionLineId = vehicle.assignedProductionLineId;
    tooltipText = `生产线: ${productionLineId}`;
  } else {
    return null;
  }

  // 渲染角标
  return (
    <TooltipProvider delayDuration={100}>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="absolute top-0 right-0 z-10 pointer-events-none">
            {indicatorType === 'paused' && (
              <div className="flex items-center justify-center w-4 h-4 rounded-bl-md bg-orange-500 shadow-sm">
                <PauseCircleIcon className="h-2.5 w-2.5 text-white" />
              </div>
            )}
            
            {indicatorType === 'deactivated' && (
              <div className="flex items-center justify-center w-4 h-4 rounded-bl-md bg-slate-600 shadow-sm">
                <XCircleIcon className="h-2.5 w-2.5 text-white" />
              </div>
            )}
            
            {indicatorType === 'production-line' && (
              <div className="flex items-center justify-center w-4 h-4 rounded-bl-md bg-accent shadow-sm">
                {productionLineId.length <= 2 ? (
                  <span className="text-[7px] font-bold text-accent-foreground">{productionLineId}</span>
                ) : (
                  <Factory className="h-2.5 w-2.5 text-accent-foreground" />
                )}
              </div>
            )}
          </div>
        </TooltipTrigger>
        <TooltipContent side="top" align="end" className="text-xs">
          <p>{tooltipText}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};


export function VehicleCardStylerModal({
  isOpen,
  onOpenChange,
  currentStyles,
  onStylesChange,
  onVehiclesPerRowChange,
}: VehicleCardStylerModalProps) {
  const handleStyleChange = <K extends keyof InTaskVehicleCardStyle>(
    key: K,
    value: InTaskVehicleCardStyle[K]
  ) => {
    console.log('handleStyleChange called:', key, value);
    console.log('Current styles before change:', currentStyles);
    const newStyles = { ...currentStyles, [key]: value };
    console.log('New styles:', newStyles);
    onStylesChange(newStyles);
  };

  const renderPreviewCard = (
    variant: 'normal' | 'washed' | 'paused' | 'deactivated' | 'line' | 'dispatchPanelSchedulable' | 'gradient',
    index: number,
    isDispatchPanel: boolean = false
  ) => {
    let cardBaseBgColor = currentStyles.cardBgColor;
    let cardShadow = currentStyles.boxShadow;
    let customStyle: React.CSSProperties | undefined = undefined;

    let mockVehicle: Partial<Vehicle> = {
      operationalStatus: 'normal',
      assignedProductionLineId: undefined,
      lastTripWashedWithPumpWater: false,
      allowWeighRoomEdit: Math.random() > 0.5,
      productionStatus: ['queued', 'producing', 'produced', 'weighed', 'ticketed', 'shipped'][index % 6] as Vehicle['productionStatus'],
    };

    let showTopDotsPreview = !isDispatchPanel;

    switch (variant) {
      case 'washed':
        cardBaseBgColor = 'bg-red-400 dark:bg-red-500/90';
        cardShadow = 'shadow-lg shadow-red-500/70 dark:shadow-red-400/60';
        mockVehicle.lastTripWashedWithPumpWater = true;
        break;
      case 'paused':
        mockVehicle.operationalStatus = 'paused';
        cardBaseBgColor = 'bg-pink-100/90 dark:bg-pink-900/50';
        cardShadow = 'shadow-lg shadow-pink-500/30 dark:shadow-pink-700/30';
        break;
      case 'deactivated':
        mockVehicle.operationalStatus = 'deactivated';
        cardBaseBgColor = 'bg-pink-100/90 dark:bg-pink-900/50';
        cardShadow = 'shadow-lg shadow-pink-500/30 dark:shadow-pink-700/30';
        break;
      case 'line':
        mockVehicle.assignedProductionLineId = 'L1';
        showTopDotsPreview = true;
        break;
      case 'dispatchPanelSchedulable':
        cardBaseBgColor = 'bg-pink-100/90 dark:bg-pink-900/50';
        cardShadow = 'shadow-lg shadow-pink-500/30 dark:shadow-pink-700/30';
        showTopDotsPreview = false;
        break;
      case 'gradient':
      case 'normal': // normal will also try to apply gradient if set
        // 使用新的背景设置组件
        const backgroundStyle = getVehicleCardBackgroundStyle(currentStyles);
        if (backgroundStyle.style) {
          customStyle = backgroundStyle.style;
          cardBaseBgColor = ''; // 清空背景类，使用内联样式
        } else {
          cardBaseBgColor = backgroundStyle.className;
        }
        break;
    }

    const finalBgClass = cardBaseBgColor;


    const statusDotBaseClass = "rounded-full shadow-inner";
    const statusDot3DEffect = "relative after:absolute after:inset-0 after:rounded-full after:bg-gradient-to-br after:from-white/30 after:to-transparent after:opacity-50 before:absolute before:-inset-px before:rounded-full before:border before:border-black/10";


    return (
      <div
        key={`preview-card-${variant}-${index}`}
        className={cn(
          "flex flex-col justify-between p-0.5 border relative overflow-hidden",
          currentStyles.cardWidth,
          currentStyles.cardHeight,
          finalBgClass,
          currentStyles.borderRadius,
          cardShadow
        )}
        style={customStyle}
      >
        <PreviewCornerRibbonIndicator vehicle={mockVehicle} isDispatchPanelView={isDispatchPanel} />
        {showTopDotsPreview && (
          <div className={cn("flex items-center justify-between w-full px-0.5 pt-0.5", currentStyles.statusDotSize || 'mb-px')}>
            <div className="flex items-center space-x-0.5">
              <div title={`生产状态: ${mockVehicle.productionStatus} (示例)`} className={cn(statusDotBaseClass, currentStyles.statusDotSize, getProductionStatusColor(mockVehicle.productionStatus as Vehicle['productionStatus']), statusDot3DEffect)} />
              {mockVehicle.allowWeighRoomEdit &&
                <div title="允许磅房修改 (示例)" className={cn(statusDotBaseClass, currentStyles.statusDotSize, "bg-teal-500", statusDot3DEffect)} />
              }
            </div>
          </div>
        )}
        {!showTopDotsPreview && <div className={cn(currentStyles.statusDotSize || 'h-1', "mb-px")}></div>}

        <div className='flex-1 flex items-center justify-center px-0.5 overflow-hidden min-w-0 w-full'>
          <span className={cn(
            "truncate w-full text-center",
            currentStyles.fontSize,
            currentStyles.fontColor,
            currentStyles.vehicleNumberFontWeight
          )}>
            {variant.charAt(0).toUpperCase() + variant.slice(1).replace('dispatchPanelSchedulable', '调度面板')}
          </span>
        </div>
         <div className={cn(currentStyles.statusDotSize || 'h-1', "mb-px")}></div>
      </div>
    );
  };


  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>自定义调度车辆卡片样式</DialogTitle>
          <DialogDescription>
            调整车辆卡片的视觉外观。这些样式将应用于所有车辆卡片。
          </DialogDescription>
        </DialogHeader>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 py-4">

          <div className="space-y-1.5">
            <Label htmlFor="cardWidth">卡片宽度</Label>
            <Select
              value={currentStyles.cardWidth}
              onValueChange={(value) => handleStyleChange('cardWidth', value as InTaskVehicleCardStyle['cardWidth'])}
            >
              <SelectTrigger id="cardWidth"><SelectValue /></SelectTrigger>
              <SelectContent>
                {cardWidthOptions.map((opt) => (
                  <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-1.5">
            <Label htmlFor="cardHeight">卡片高度</Label>
            <Select
              value={currentStyles.cardHeight}
              onValueChange={(value) => handleStyleChange('cardHeight', value as InTaskVehicleCardStyle['cardHeight'])}
            >
              <SelectTrigger id="cardHeight"><SelectValue /></SelectTrigger>
              <SelectContent>
                {cardHeightOptions.map((opt) => (
                  <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-1.5">
            <Label htmlFor="vehiclesPerRow">每行显示车辆数 (任务列表)</Label>
            <Select
              value={(currentStyles.vehiclesPerRow || 4).toString()}
              onValueChange={(value) => {
                const numValue = parseInt(value) as NonNullable<InTaskVehicleCardStyle['vehiclesPerRow']>;
                handleStyleChange('vehiclesPerRow', numValue);
                if (onVehiclesPerRowChange) {
                  onVehiclesPerRowChange(numValue);
                }
              }}
            >
              <SelectTrigger id="vehiclesPerRow"><SelectValue /></SelectTrigger>
              <SelectContent>
                {vehiclesPerRowOptions.map((opt) => (
                  <SelectItem key={opt.value} value={opt.value.toString()}>{opt.label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-1.5">
            <Label htmlFor="fontSize">车号字体大小</Label>
            <Select
              value={currentStyles.fontSize}
              onValueChange={(value) => handleStyleChange('fontSize', value as InTaskVehicleCardStyle['fontSize'])}
            >
              <SelectTrigger id="fontSize"><SelectValue /></SelectTrigger>
              <SelectContent>
                {fontSizes.map((sizeOpt) => (
                  <SelectItem key={sizeOpt.value} value={sizeOpt.value}>{sizeOpt.label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-1.5">
            <Label htmlFor="fontColor">车号字体颜色</Label>
            <Select
              value={currentStyles.fontColor}
              onValueChange={(value) => handleStyleChange('fontColor', value)}
            >
              <SelectTrigger id="fontColor"><SelectValue /></SelectTrigger>
              <SelectContent>
                {fontColors.map((color) => (
                  <SelectItem key={color.value} value={color.value}>
                     <span className="flex items-center">
                      <span className={cn("w-4 h-4 rounded-full mr-2 border", getPreviewColorClass(color.value))} ></span>
                      {color.label}
                    </span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-1.5">
            <Label htmlFor="vehicleNumberFontWeight">车号字重</Label>
            <Select
              value={currentStyles.vehicleNumberFontWeight}
              onValueChange={(value) => handleStyleChange('vehicleNumberFontWeight', value as InTaskVehicleCardStyle['vehicleNumberFontWeight'])}
            >
              <SelectTrigger id="vehicleNumberFontWeight"><SelectValue /></SelectTrigger>
              <SelectContent>
                {vehicleNumberFontWeightOptions.map((opt) => (
                  <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 车卡背景色设置 */}
          <div className="md:col-span-2">
            <VehicleCardBackgroundSettings
              currentStyles={currentStyles}
              onStylesChange={onStylesChange}
            />
          </div>

          <div className="space-y-1.5">
            <Label htmlFor="statusDotSize">顶部状态指示点大小</Label>
            <Select
              value={currentStyles.statusDotSize}
              onValueChange={(value) => handleStyleChange('statusDotSize', value as InTaskVehicleCardStyle['statusDotSize'])}
            >
              <SelectTrigger id="statusDotSize"><SelectValue /></SelectTrigger>
              <SelectContent>
                {statusDotSizeOptions.map((opt) => (
                  <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-1.5">
            <Label htmlFor="borderRadius">圆角半径</Label>
            <Select
              value={currentStyles.borderRadius}
              onValueChange={(value) => handleStyleChange('borderRadius', value as InTaskVehicleCardStyle['borderRadius'])}
            >
              <SelectTrigger id="borderRadius"><SelectValue /></SelectTrigger>
              <SelectContent>
                {borderRadiusOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-1.5">
            <Label htmlFor="boxShadow">阴影</Label>
            <Select
              value={currentStyles.boxShadow}
              onValueChange={(value) => handleStyleChange('boxShadow', value as InTaskVehicleCardStyle['boxShadow'])}
            >
              <SelectTrigger id="boxShadow"><SelectValue /></SelectTrigger>
              <SelectContent>
                {boxShadowOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="md:col-span-2 mt-2 p-4 border rounded-md bg-muted/30 flex flex-col items-center space-y-2">
            <div className="text-sm text-muted-foreground mb-2">样式预览区:</div>
            <div className="grid grid-cols-3 sm:grid-cols-4 max-w-full overflow-x-auto p-2">
              {renderPreviewCard('gradient', 0, false)}
              {renderPreviewCard('dispatchPanelSchedulable', 1, true)}
              {renderPreviewCard('normal', 2, false)}
              {renderPreviewCard('washed', 3, false)}
              {renderPreviewCard('paused', 4, false)}
              {renderPreviewCard('deactivated', 5, false)}
              {renderPreviewCard('line', 6, false)}
            </div>
            <div className="text-xs text-muted-foreground mt-2 text-center">
                注: 实际颜色和状态指示会根据车辆具体状态动态变化。<br/>
                调度面板中的车辆不显示顶部状态指示灯。右上角徽标优先显示 暂停/停用。
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

