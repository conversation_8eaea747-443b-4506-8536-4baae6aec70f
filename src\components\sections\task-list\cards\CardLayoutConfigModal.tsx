// src/components/sections/task-list/cards/CardLayoutConfigModal.tsx
'use client';

import React, { useState, useCallback, useRef } from 'react';
import { useDrag, useDrop, DropTargetMonitor } from 'react-dnd';
import type { Identifier, XYCoord } from 'dnd-core';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import {
  GripVertical,
  Eye,
  EyeOff,
  Settings2,
  Layout,
  Palette,
  Move3D,
  Hash,
  Building2,
  User,
  AlertTriangle,
  Target,
  Droplets,
  Calendar,
  Timer,
  Phone,
  FileText,
  MapPin,
  Clock,
} from 'lucide-react';
import { ItemTypes } from '@/constants/dndItemTypes';

// FieldConfig interface remains the same
interface FieldConfig {
  id: string;
  label: string;
  visible: boolean;
  order: number;
  size?: 'small' | 'medium' | 'large';
  color?: string;
  weight?: 'normal' | 'medium' | 'bold';
}

// CardLayoutConfig interface remains the same
interface CardLayoutConfig {
  topFields: FieldConfig[];
  middleFields: FieldConfig[];
  showVehicleArea: boolean;
  cardSize: 'small' | 'medium' | 'large';
  spacing: 'tight' | 'normal' | 'loose';
  theme: 'default' | 'modern' | 'glass' | 'gradient';
}

interface CardLayoutConfigModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  config: CardLayoutConfig;
  onConfigChange: (config: CardLayoutConfig) => void;
  onSave: (config: CardLayoutConfig) => void;
  onCancel: () => void;
}

// AVAILABLE_FIELDS and FIELD_ICONS remain the same
const AVAILABLE_FIELDS: FieldConfig[] = [
  { id: 'taskNumber', label: '任务编号', visible: true, order: 0 },
  { id: 'constructionSite', label: '施工地点', visible: true, order: 1 },
  { id: 'customerName', label: '客户名称', visible: true, order: 2 },
  { id: 'dispatchStatus', label: '发车状态', visible: true, order: 3 },
  { id: 'requiredVolume', label: '需求方量', visible: true, order: 4 },
  { id: 'completedVolume', label: '完成方量', visible: true, order: 5 },
  { id: 'progress', label: '完成进度', visible: true, order: 6 },
  { id: 'scheduledTime', label: '计划时间', visible: true, order: 7 },
  { id: 'estimatedDuration', label: '预计时长', visible: true, order: 8 },
  { id: 'contactPhone', label: '联系电话', visible: false, order: 9 },
  { id: 'notes', label: '备注信息', visible: false, order: 10 },
  { id: 'address', label: '详细地址', visible: false, order: 11 },
  { id: 'createdAt', label: '创建时间', visible: false, order: 12 },
];

const FIELD_ICONS: Record<string, React.ReactNode> = {
  taskNumber: <Hash className="w-4 h-4" />,
  constructionSite: <Building2 className="w-4 h-4" />,
  customerName: <User className="w-4 h-4" />,
  dispatchStatus: <AlertTriangle className="w-4 h-4" />,
  requiredVolume: <Target className="w-4 h-4" />,
  completedVolume: <Droplets className="w-4 h-4" />,
  progress: <Target className="w-4 h-4" />,
  scheduledTime: <Calendar className="w-4 h-4" />,
  estimatedDuration: <Timer className="w-4 h-4" />,
  contactPhone: <Phone className="w-4 h-4" />,
  notes: <FileText className="w-4 h-4" />,
  address: <MapPin className="w-4 h-4" />,
  createdAt: <Clock className="w-4 h-4" />,
};


interface DraggableConfigFieldItemProps {
  field: FieldConfig;
  index: number;
  section: 'topFields' | 'middleFields';
  moveField: (dragIndex: number, hoverIndex: number, section: 'topFields' | 'middleFields') => void;
  toggleFieldVisibility: (fieldId: string, section: 'topFields' | 'middleFields') => void;
  removeFieldFromSection: (fieldId: string, section: 'topFields' | 'middleFields') => void;
}

interface DragItem {
  id: string;
  index: number;
  section: 'topFields' | 'middleFields';
  type: typeof ItemTypes.CONFIG_FIELD_ITEM;
}

const DraggableConfigFieldItem: React.FC<DraggableConfigFieldItemProps> = ({
  field,
  index,
  section,
  moveField,
  toggleFieldVisibility,
  removeFieldFromSection,
}) => {
  const ref = useRef<HTMLDivElement>(null);

  const [{ handlerId }, drop] = useDrop<
    DragItem,
    void,
    { handlerId: Identifier | null }
  >({
    accept: ItemTypes.CONFIG_FIELD_ITEM,
    canDrop: (item) => item.section === section,
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      };
    },
    hover(item: DragItem, monitor: DropTargetMonitor) {
      if (!ref.current) {
        return;
      }
      if (item.section !== section) return;

      const dragIndex = item.index;
      const hoverIndex = index;

      if (dragIndex === hoverIndex) {
        return;
      }

      const hoverBoundingRect = ref.current?.getBoundingClientRect();
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      const clientOffset = monitor.getClientOffset();
      const hoverClientY = (clientOffset as XYCoord).y - hoverBoundingRect.top;

      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return;
      }
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return;
      }

      moveField(dragIndex, hoverIndex, section);
      item.index = hoverIndex;
    },
  });

  const [{ isDragging }, drag, preview] = useDrag({
    type: ItemTypes.CONFIG_FIELD_ITEM,
    item: (): DragItem => ({ id: field.id, index, section, type: ItemTypes.CONFIG_FIELD_ITEM }),
    collect: (monitor: any) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  preview(drop(ref)); // Attach both drag and drop to the ref

  return (
    <div
      ref={ref}
      data-handler-id={handlerId}
      className={cn(
        "flex items-center gap-3 p-2 bg-background border rounded-md",
        isDragging ? "opacity-40 shadow-lg ring-2 ring-primary/50" : "shadow-sm",
        "cursor-grab active:cursor-grabbing"
      )}
    >
      <div ref={drag} className="cursor-grab active:cursor-grabbing"> {/* Drag handle is the GripVertical icon itself */}
        <GripVertical className="w-4 h-4 text-muted-foreground" />
      </div>
      <div className="flex-shrink-0">
        {FIELD_ICONS[field.id] || <FileText className="w-4 h-4" />}
      </div>
      <div className="flex-1 min-w-0">
        <div className="font-medium text-sm truncate">{field.label}</div>
        <div className="text-xs text-muted-foreground">{field.id}</div>
      </div>
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => toggleFieldVisibility(field.id, section)}
          className="h-6 w-6 p-0"
        >
          {field.visible ? <Eye className="w-3 h-3" /> : <EyeOff className="w-3 h-3" />}
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => removeFieldFromSection(field.id, section)}
          className="h-6 w-6 p-0 text-destructive hover:text-destructive"
        >
          ×
        </Button>
      </div>
    </div>
  );
};


export const CardLayoutConfigModal: React.FC<CardLayoutConfigModalProps> = ({
  open,
  onOpenChange,
  config,
  onConfigChange,
  onSave,
  onCancel,
}) => {
  const [localConfig, setLocalConfig] = useState<CardLayoutConfig>(config);

  // Ensure config prop changes are reflected in localConfig
  useEffect(() => {
    setLocalConfig(config);
  }, [config]);

  const moveField = useCallback(
    (dragIndex: number, hoverIndex: number, section: 'topFields' | 'middleFields') => {
      setLocalConfig((prevConfig) => {
        const currentFields = [...prevConfig[section]];
        const [draggedItem] = currentFields.splice(dragIndex, 1);
        currentFields.splice(hoverIndex, 0, draggedItem);
        const updatedFields = currentFields.map((f, idx) => ({ ...f, order: idx }));
        return { ...prevConfig, [section]: updatedFields };
      });
    },
    []
  );

  const toggleFieldVisibility = useCallback((fieldId: string, section: 'topFields' | 'middleFields') => {
    setLocalConfig(prev => ({
      ...prev,
      [section]: prev[section].map(field =>
        field.id === fieldId ? { ...field, visible: !field.visible } : field
      ),
    }));
  }, []);

  const addFieldToSection = useCallback((fieldId: string, section: 'topFields' | 'middleFields') => {
    const availableField = AVAILABLE_FIELDS.find(f => f.id === fieldId);
    if (!availableField) return;

    const maxOrder = Math.max(...localConfig[section].map(f => f.order), -1);
    const newField: FieldConfig = {
      ...availableField,
      order: maxOrder + 1,
      visible: true,
    };

    setLocalConfig(prev => ({
      ...prev,
      [section]: [...prev[section], newField],
    }));
  }, [localConfig]);

  const removeFieldFromSection = useCallback((fieldId: string, section: 'topFields' | 'middleFields') => {
    setLocalConfig(prev => ({
      ...prev,
      [section]: prev[section].filter(field => field.id !== fieldId),
    }));
  }, []);


  const renderFieldList = useCallback((fields: FieldConfig[], section: 'topFields' | 'middleFields', maxFields: number) => {
    const droppableId = `${section}-list`;
    const [{ isOverCurrent, canDropCurrent }, dropListRef] = useDrop({
        accept: ItemTypes.CONFIG_FIELD_ITEM, // Only accept config field items for the list background itself
        canDrop: (item: DragItem) => item.section === section && fields.length < maxFields, // Allow drop on empty list if not full
        drop: (item: DragItem, monitor) => {
            // This handles dropping an item into an empty list or at the end
            if (!monitor.didDrop() && item.section === section && fields.length < maxFields) {
                // Logic for adding/moving item to the end of this list if it's coming from available fields or reordering
                // This part is complex as 'item' might be from available fields or another part of the list.
                // For simplicity with react-dnd, usually item-to-item reordering is easier.
                // If we only allow reordering existing items, this drop on the list itself might not be needed.
            }
        },
        collect: monitor => ({
            isOverCurrent: monitor.isOver(),
            canDropCurrent: monitor.canDrop(),
        }),
    });

    return (
      <div
        ref={dropListRef}
        className={cn(
          "space-y-2 min-h-[100px] p-3 border-2 border-dashed rounded-lg transition-colors",
          isOverCurrent && canDropCurrent ? "border-primary bg-primary/5" : "border-muted-foreground/30"
        )}
      >
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <span>{section === 'topFields' ? '顶部字段' : '中间字段'} ({fields.length}/{maxFields})</span>
          {fields.length >= maxFields && <Badge variant="secondary" className="text-xs">已满</Badge>}
        </div>
        {fields.sort((a,b) => a.order - b.order).map((field, index) => (
          <DraggableConfigFieldItem
            key={field.id}
            field={field}
            index={index}
            section={section}
            moveField={moveField}
            toggleFieldVisibility={toggleFieldVisibility}
            removeFieldFromSection={removeFieldFromSection}
          />
        ))}
        {fields.length === 0 && (
          <div className="text-center text-sm text-muted-foreground py-4">
            从右侧拖拽字段到此处
          </div>
        )}
      </div>
    );
  }, [moveField, toggleFieldVisibility, removeFieldFromSection]);


  const getUnusedFields = useCallback(() => {
    const usedFieldIds = new Set([
      ...localConfig.topFields.map(f => f.id),
      ...localConfig.middleFields.map(f => f.id),
    ]);
    return AVAILABLE_FIELDS.filter(field => !usedFieldIds.has(field.id));
  }, [localConfig]);

  const handleSaveConfig = () => {
    onConfigChange(localConfig); // Update parent's view immediately if needed
    onSave(localConfig);      // Persist
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Layout className="w-5 h-5" />
            卡片布局配置
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="layout" className="flex-1 flex flex-col overflow-hidden">
          <TabsList className="grid w-full grid-cols-3 shrink-0">
            <TabsTrigger value="layout" className="flex items-center gap-2"><Move3D className="w-4 h-4" />字段布局</TabsTrigger>
            <TabsTrigger value="appearance" className="flex items-center gap-2"><Palette className="w-4 h-4" />外观设置</TabsTrigger>
            <TabsTrigger value="preview" className="flex items-center gap-2"><Eye className="w-4 h-4" />预览效果</TabsTrigger>
          </TabsList>

          <TabsContent value="layout" className="flex-1 overflow-y-auto p-1">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 h-full">
              <div className="space-y-4 md:col-span-1">
                <h3 className="font-semibold text-lg">可用字段</h3>
                <div className="space-y-2 max-h-[calc(80vh-200px)] overflow-y-auto p-1 border rounded-lg">
                  {getUnusedFields().map((field) => (
                    <div
                      key={field.id}
                      className="flex items-center gap-3 p-2 bg-muted/50 border rounded-md"
                    >
                      <div className="flex-shrink-0">
                        {FIELD_ICONS[field.id] || <FileText className="w-4 h-4" />}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm">{field.label}</div>
                        <div className="text-xs text-muted-foreground">{field.id}</div>
                      </div>
                      <div className="flex gap-1">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => addFieldToSection(field.id, 'topFields')}
                          disabled={localConfig.topFields.length >= 4}
                          className="h-6 px-2 text-xs"
                        >
                          顶部
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => addFieldToSection(field.id, 'middleFields')}
                          disabled={localConfig.middleFields.length >= 6}
                          className="h-6 px-2 text-xs"
                        >
                          中间
                        </Button>
                      </div>
                    </div>
                  ))}
                   {getUnusedFields().length === 0 && <p className="text-sm text-muted-foreground text-center py-4">所有字段已分配</p>}
                </div>
              </div>
              <div className="space-y-4 md:col-span-1">
                <h3 className="font-semibold text-lg">顶部字段 (最多4个)</h3>
                <div className="max-h-[calc(80vh-200px)] overflow-y-auto p-1">
                    {renderFieldList(localConfig.topFields, 'topFields', 4)}
                </div>
              </div>
              <div className="space-y-4 md:col-span-1">
                <h3 className="font-semibold text-lg">中间字段 (最多6个)</h3>
                 <div className="max-h-[calc(80vh-200px)] overflow-y-auto p-1">
                    {renderFieldList(localConfig.middleFields, 'middleFields', 6)}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="appearance" className="space-y-6 flex-1 overflow-y-auto p-1">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="font-semibold">卡片设置</h3>
                <div className="space-y-2">
                  <Label>卡片尺寸</Label>
                  <div className="grid grid-cols-3 gap-2">
                    {(['small', 'medium', 'large'] as const).map((size) => (
                      <Button key={size} variant={localConfig.cardSize === size ? 'default' : 'outline'} size="sm" onClick={() => setLocalConfig(prev => ({ ...prev, cardSize: size }))}>
                        {size === 'small' ? '小' : size === 'medium' ? '中' : '大'}
                      </Button>
                    ))}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>间距设置</Label>
                  <div className="grid grid-cols-3 gap-2">
                    {(['tight', 'normal', 'loose'] as const).map((spacing) => (
                      <Button key={spacing} variant={localConfig.spacing === spacing ? 'default' : 'outline'} size="sm" onClick={() => setLocalConfig(prev => ({ ...prev, spacing }))}>
                        {spacing === 'tight' ? '紧凑' : spacing === 'normal' ? '标准' : '宽松'}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <h3 className="font-semibold">主题设置</h3>
                <div className="space-y-2">
                  <Label>卡片主题</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {(['default', 'modern', 'glass', 'gradient'] as const).map((theme) => (
                      <Button key={theme} variant={localConfig.theme === theme ? 'default' : 'outline'} size="sm" onClick={() => setLocalConfig(prev => ({ ...prev, theme }))}>
                        {theme.charAt(0).toUpperCase() + theme.slice(1)}
                      </Button>
                    ))}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="show-vehicle-area" checked={localConfig.showVehicleArea} onCheckedChange={(checked) => setLocalConfig(prev => ({ ...prev, showVehicleArea: checked }))} />
                  <Label htmlFor="show-vehicle-area">显示车辆区域</Label>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="preview" className="space-y-4 flex-1 overflow-y-auto p-1">
            <div className="p-4 border rounded-lg bg-muted/20">
              <h4 className="font-medium mb-3">配置预览</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between"><span>卡片尺寸:</span><span>{localConfig.cardSize}</span></div>
                <div className="flex justify-between"><span>间距:</span><span>{localConfig.spacing}</span></div>
                <div className="flex justify-between"><span>主题:</span><span>{localConfig.theme}</span></div>
                <div className="flex justify-between"><span>车辆区域:</span><span>{localConfig.showVehicleArea ? '显示' : '隐藏'}</span></div>
                <div className="flex justify-between"><span>顶部字段:</span><span>{localConfig.topFields.filter(f => f.visible).length} 个</span></div>
                <div className="flex justify-between"><span>中间字段:</span><span>{localConfig.middleFields.filter(f => f.visible).length} 个</span></div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex items-center justify-between pt-4 border-t shrink-0">
          <div className="text-sm text-muted-foreground">
            顶部: {localConfig.topFields.filter(f => f.visible).length}/4 字段 | 中间: {localConfig.middleFields.filter(f => f.visible).length}/6 字段
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={onCancel}>取消</Button>
            <Button onClick={handleSaveConfig}>保存配置</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
