// src/components/modals/vehicle-card-background-settings.tsx
'use client';

import { useState, useEffect } from 'react';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { cn } from '@/lib/utils';
import type { InTaskVehicleCardStyle } from '@/types';

interface VehicleCardBackgroundSettingsProps {
  currentStyles: InTaskVehicleCardStyle;
  onStylesChange: (styles: InTaskVehicleCardStyle) => void;
}

// 预设渐变选项
const GRADIENT_OPTIONS = [
  { value: 'none', label: '无渐变', className: 'bg-card' },
  { value: 'bg-gradient-to-r from-blue-400 to-purple-500', label: '蓝紫渐变', className: 'bg-gradient-to-r from-blue-400 to-purple-500' },
  { value: 'bg-gradient-to-r from-green-400 to-blue-500', label: '绿蓝渐变', className: 'bg-gradient-to-r from-green-400 to-blue-500' },
  { value: 'bg-gradient-to-r from-purple-400 to-pink-400', label: '紫粉渐变', className: 'bg-gradient-to-r from-purple-400 to-pink-400' },
  { value: 'bg-gradient-to-r from-yellow-400 to-red-500', label: '黄红渐变', className: 'bg-gradient-to-r from-yellow-400 to-red-500' },
  { value: 'bg-gradient-to-r from-indigo-400 to-cyan-400', label: '靛青渐变', className: 'bg-gradient-to-r from-indigo-400 to-cyan-400' },
  { value: 'bg-gradient-to-br from-pink-400 to-red-400', label: '粉红对角', className: 'bg-gradient-to-br from-pink-400 to-red-400' },
  { value: 'bg-gradient-to-br from-green-400 to-yellow-400', label: '绿黄对角', className: 'bg-gradient-to-br from-green-400 to-yellow-400' },
];

// 渐变方向选项
const GRADIENT_DIRECTIONS = [
  { value: 'to-r', label: '向右' },
  { value: 'to-l', label: '向左' },
  { value: 'to-t', label: '向上' },
  { value: 'to-b', label: '向下' },
  { value: 'to-tr', label: '右上' },
  { value: 'to-tl', label: '左上' },
  { value: 'to-br', label: '右下' },
  { value: 'to-bl', label: '左下' },
  { value: 'radial', label: '径向' },
];

// 颜色选项
const COLOR_OPTIONS = [
  { value: '#3b82f6', label: '蓝色' },
  { value: '#ef4444', label: '红色' },
  { value: '#10b981', label: '绿色' },
  { value: '#f59e0b', label: '黄色' },
  { value: '#8b5cf6', label: '紫色' },
  { value: '#ec4899', label: '粉色' },
  { value: '#06b6d4', label: '青色' },
  { value: '#84cc16', label: '柠檬绿' },
  { value: '#f97316', label: '橙色' },
  { value: '#6366f1', label: '靛蓝' },
  { value: '#14b8a6', label: '蓝绿' },
  { value: '#a855f7', label: '紫罗兰' },
];

// 单色背景选项
const SOLID_COLOR_OPTIONS = [
  { value: 'bg-card/80', label: '默认卡片色', className: 'bg-card/80' },
  { value: 'bg-blue-50 dark:bg-blue-900/20', label: '浅蓝色', className: 'bg-blue-50 dark:bg-blue-900/20' },
  { value: 'bg-green-50 dark:bg-green-900/20', label: '浅绿色', className: 'bg-green-50 dark:bg-green-900/20' },
  { value: 'bg-yellow-50 dark:bg-yellow-900/20', label: '浅黄色', className: 'bg-yellow-50 dark:bg-yellow-900/20' },
  { value: 'bg-purple-50 dark:bg-purple-900/20', label: '浅紫色', className: 'bg-purple-50 dark:bg-purple-900/20' },
  { value: 'bg-pink-50 dark:bg-pink-900/20', label: '浅粉色', className: 'bg-pink-50 dark:bg-pink-900/20' },
  { value: 'bg-gray-50 dark:bg-gray-900/20', label: '浅灰色', className: 'bg-gray-50 dark:bg-gray-900/20' },
];

// 生成自定义渐变样式
export const generateCustomGradient = (styles: InTaskVehicleCardStyle): { className: string; style?: React.CSSProperties } => {
  if (!styles.gradientEnabled || !styles.gradientDirection || !styles.gradientStartColor || !styles.gradientEndColor) {
    return { className: '' };
  }

  const direction = styles.gradientDirection;
  const startColor = styles.gradientStartColor;
  const endColor = styles.gradientEndColor;

  // 将Tailwind方向转换为CSS渐变方向
  const directionMap: Record<string, string> = {
    'to-r': 'to right',
    'to-l': 'to left',
    'to-t': 'to top',
    'to-b': 'to bottom',
    'to-tr': 'to top right',
    'to-tl': 'to top left',
    'to-br': 'to bottom right',
    'to-bl': 'to bottom left',
    'radial': 'radial-gradient'
  };

  const cssDirection = directionMap[direction] || 'to right';
  
  if (direction === 'radial') {
    return {
      className: '',
      style: {
        background: `radial-gradient(circle, ${startColor}, ${endColor})`
      }
    };
  } else {
    return {
      className: '',
      style: {
        background: `linear-gradient(${cssDirection}, ${startColor}, ${endColor})`
      }
    };
  }
};

// 获取车卡背景样式
export const getVehicleCardBackgroundStyle = (styles: InTaskVehicleCardStyle): { className: string; style?: React.CSSProperties } => {
  // 优先使用自定义渐变
  if (styles.gradientEnabled) {
    const customGradient = generateCustomGradient(styles);
    if (customGradient.style) {
      return customGradient;
    }
  }
  
  // 其次使用预设渐变
  if (styles.cardGradient && styles.cardGradient !== 'none') {
    return { className: styles.cardGradient };
  }
  
  // 最后使用单色背景
  return { className: styles.cardBgColor || 'bg-card/80' };
};

export function VehicleCardBackgroundSettings({
  currentStyles,
  onStylesChange,
}: VehicleCardBackgroundSettingsProps) {
  // 使用本地状态来管理gradientEnabled
  const [localGradientEnabled, setLocalGradientEnabled] = useState(currentStyles.gradientEnabled || false);

  // 当currentStyles变化时，同步本地状态
  useEffect(() => {
    setLocalGradientEnabled(currentStyles.gradientEnabled || false);
  }, [currentStyles.gradientEnabled]);

  const handleStyleChange = <K extends keyof InTaskVehicleCardStyle>(
    key: K,
    value: InTaskVehicleCardStyle[K]
  ) => {
    console.log('VehicleCardBackgroundSettings handleStyleChange:', key, value);
    onStylesChange({ ...currentStyles, [key]: value });
  };

  const handleGradientEnabledChange = (checked: boolean) => {
    console.log('handleGradientEnabledChange called:', checked);
    setLocalGradientEnabled(checked);
    handleStyleChange('gradientEnabled', checked);
    // 如果启用自定义渐变，清除预设渐变
    if (checked) {
      handleStyleChange('cardGradient', undefined);
    }
  };

  // 获取当前背景样式用于预览
  const currentBackgroundStyle = getVehicleCardBackgroundStyle(currentStyles);

  return (
    <div className="space-y-4">
      {/* 背景预览 */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">背景预览</Label>
        <div 
          className={cn(
            "w-full h-16 border rounded-md flex items-center justify-center text-sm",
            currentBackgroundStyle.className
          )}
          style={currentBackgroundStyle.style}
        >
          预览效果
        </div>
      </div>

      {/* 单色背景选择 */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">单色背景</Label>
        <Select
          value={currentStyles.cardBgColor || 'bg-card/80'}
          onValueChange={(value) => {
            handleStyleChange('cardBgColor', value);
            // 选择单色背景时，清除渐变设置
            handleStyleChange('cardGradient', undefined);
            setLocalGradientEnabled(false);
            handleStyleChange('gradientEnabled', false);
          }}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {SOLID_COLOR_OPTIONS.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                <div className="flex items-center space-x-2">
                  <div className={cn("w-4 h-4 rounded border", option.className)} />
                  <span>{option.label}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* 预设渐变选择 */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">预设渐变</Label>
        <Select
          value={currentStyles.cardGradient || 'none'}
          onValueChange={(value) => {
            handleStyleChange('cardGradient', value === 'none' ? undefined : value);
            // 选择预设渐变时，禁用自定义渐变
            if (value !== 'none') {
              setLocalGradientEnabled(false);
              handleStyleChange('gradientEnabled', false);
            }
          }}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {GRADIENT_OPTIONS.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                <div className="flex items-center space-x-2">
                  <div className={cn("w-4 h-4 rounded border", option.className)} />
                  <span>{option.label}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* 自定义渐变 */}
      <div className="space-y-3">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="gradientEnabled"
            checked={localGradientEnabled}
            onCheckedChange={handleGradientEnabledChange}
          />
          <Label 
            htmlFor="gradientEnabled" 
            className="text-sm font-medium cursor-pointer"
            onClick={() => handleGradientEnabledChange(!localGradientEnabled)}
          >
            启用自定义渐变
          </Label>
        </div>

        {localGradientEnabled && (
          <div className="space-y-3 pl-6 border-l-2 border-muted">
            {/* 渐变方向 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">渐变方向</Label>
              <Select
                value={currentStyles.gradientDirection || 'to-r'}
                onValueChange={(value) => handleStyleChange('gradientDirection', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {GRADIENT_DIRECTIONS.map((direction) => (
                    <SelectItem key={direction.value} value={direction.value}>
                      {direction.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 开始颜色 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">开始颜色</Label>
              <Select
                value={currentStyles.gradientStartColor || '#3b82f6'}
                onValueChange={(value) => handleStyleChange('gradientStartColor', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {COLOR_OPTIONS.map((color) => (
                    <SelectItem key={color.value} value={color.value}>
                      <div className="flex items-center space-x-2">
                        <div 
                          className="w-4 h-4 rounded border" 
                          style={{ backgroundColor: color.value }}
                        />
                        <span>{color.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 结束颜色 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">结束颜色</Label>
              <Select
                value={currentStyles.gradientEndColor || '#8b5cf6'}
                onValueChange={(value) => handleStyleChange('gradientEndColor', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {COLOR_OPTIONS.map((color) => (
                    <SelectItem key={color.value} value={color.value}>
                      <div className="flex items-center space-x-2">
                        <div 
                          className="w-4 h-4 rounded border" 
                          style={{ backgroundColor: color.value }}
                        />
                        <span>{color.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
