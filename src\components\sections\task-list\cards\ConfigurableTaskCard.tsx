'use client';

import React, { useMemo } from 'react';
import { useDrop } from 'react-dnd';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import {
  Clock,
  Truck,
  AlertTriangle,
  CheckCircle2,
  Pause,
  XCircle,
  Target,
  Factory
} from 'lucide-react';
import { differenceInSeconds } from 'date-fns';
import type { Task, Vehicle, VehicleDisplayMode, InTaskVehicleCardStyle } from '@/types';
import { ItemTypes } from '@/constants/dndItemTypes';
import { InTaskVehicleCard } from '../in-task-vehicle-card';
import { TaskCardConfig, FieldStyle } from '@/types/taskCardConfig';

/**
 * 生产线拖拽区域组件
 */
interface ProductionLineDropZoneProps {
  lineId: string;
  lineNumber: number;
  taskId: string;
  onDropVehicle?: (vehicle: Vehicle, taskId: string, lineId: string) => void;
}

const ProductionLineDropZone: React.FC<ProductionLineDropZoneProps> = ({
  lineId,
  lineNumber,
  taskId,
  onDropVehicle
}) => {
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: ItemTypes.VEHICLE_CARD_DISPATCH,
    drop: (item: any, monitor) => {
      if (monitor.didDrop()) return;

      if (item.vehicle && onDropVehicle) {
        onDropVehicle(item.vehicle, taskId, lineId);
      }
    },
    canDrop: () => true,
    collect: (monitor) => ({
      isOver: monitor.isOver({ shallow: true }),
      canDrop: monitor.canDrop(),
    }),
  });

  return (
    <div
      ref={drop as any}
      className={cn(
        'w-full h-14 rounded-xl border-2 border-dashed transition-all duration-300',
        'flex items-center justify-between px-4 text-sm font-medium cursor-pointer',
        'transform transition-transform hover:scale-102',
        'relative overflow-hidden',
        isOver && canDrop
          ? 'border-green-400 bg-gradient-to-r from-green-50 to-green-100 text-green-700 scale-105 shadow-xl border-solid'
          : 'border-slate-300 bg-gradient-to-r from-slate-50 to-slate-100 text-slate-600 hover:border-blue-400 hover:from-blue-50 hover:to-blue-100 hover:text-blue-700'
      )}
    >
      {/* 背景动画效果 */}
      {isOver && canDrop && (
        <div className="absolute inset-0 bg-gradient-to-r from-green-200/50 to-green-300/50 animate-pulse" />
      )}

      {/* 内容 */}
      <div className="flex items-center gap-3 relative z-10">
        <div className={cn(
          'w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300',
          isOver && canDrop
            ? 'bg-green-500 shadow-lg scale-110'
            : 'bg-slate-400 hover:bg-blue-500'
        )}>
          <Factory className="w-4 h-4 text-white" />
        </div>
        <span className="font-semibold">生产线 {lineNumber}</span>
      </div>

      {/* 状态指示器 */}
      <div className="flex items-center gap-2 relative z-10">
        {isOver && canDrop ? (
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 rounded-full bg-green-500 animate-bounce" />
            <div className="w-2 h-2 rounded-full bg-green-500 animate-bounce" style={{ animationDelay: '0.1s' }} />
            <div className="w-2 h-2 rounded-full bg-green-500 animate-bounce" style={{ animationDelay: '0.2s' }} />
          </div>
        ) : (
          <div className="w-6 h-6 rounded-full border-2 border-dashed border-current opacity-50" />
        )}
      </div>
    </div>
  );
};

/**
 * 可配置任务卡片组件属性
 */
interface ConfigurableTaskCardProps {
  task: Task;
  vehicles: Vehicle[];
  config: TaskCardConfig;
  size?: 'small' | 'medium' | 'large' | 'extra-large';
  className?: string;
  vehicleDisplayMode?: VehicleDisplayMode;
  inTaskVehicleCardStyles?: InTaskVehicleCardStyle;
  density?: 'compact' | 'normal' | 'loose';
  onTaskContextMenu?: (event: React.MouseEvent, task: Task) => void;
  onTaskDoubleClick?: (task: Task) => void;
  onDropVehicleOnLine?: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  onCancelDispatch?: (vehicleId: string) => void;
  onOpenStyleEditor?: () => void;
  onOpenDeliveryOrderDetails?: (vehicleId: string, taskId: string) => void;
  onOpenVehicleContextMenu?: (event: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
}

/**
 * 获取状态图标和样式
 */
const getStatusInfo = (status: string) => {
  switch (status) {
    case 'ReadyToProduce':
      return { icon: Pause, label: '待生产', className: 'bg-yellow-100 text-yellow-700 border-yellow-200' };
    case 'InProgress':
      return { icon: Clock, label: '进行中', className: 'bg-blue-100 text-blue-700 border-blue-200' };
    case 'Completed':
      return { icon: CheckCircle2, label: '已完成', className: 'bg-green-100 text-green-700 border-green-200' };
    case 'Cancelled':
      return { icon: XCircle, label: '已取消', className: 'bg-red-100 text-red-700 border-red-200' };
    default:
      return { icon: AlertTriangle, label: '未知', className: 'bg-gray-100 text-gray-700 border-gray-200' };
  }
};

/**
 * 环形进度组件
 */
const CircularProgress: React.FC<{ percentage: number; size?: 'small' | 'medium'; style?: FieldStyle }> = ({ 
  percentage, 
  size = 'small',
  style
}) => {
  const sizeClass = size === 'small' ? 'w-8 h-8' : 'w-12 h-12';
  const strokeWidth = size === 'small' ? 2 : 3;
  const radius = size === 'small' ? 14 : 20;
  const circumference = 2 * Math.PI * radius;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  return (
    <div className={cn('relative', sizeClass)}>
      <svg className="transform -rotate-90 w-full h-full">
        <circle
          cx="50%"
          cy="50%"
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="transparent"
          className="text-gray-200"
        />
        <circle
          cx="50%"
          cy="50%"
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          className="text-blue-500 transition-all duration-300"
        />
      </svg>
      <div className="absolute inset-0 flex items-center justify-center">
        <span className={cn(
          'font-medium text-gray-700',
          size === 'small' ? 'text-xs' : 'text-sm'
        )}>
          {percentage}%
        </span>
      </div>
    </div>
  );
};

/**
 * 获取字段样式类名
 */
const getFieldStyleClasses = (style: FieldStyle): string => {
  const classes = [];
  
  // 字体大小
  switch (style.fontSize) {
    case 'xs': classes.push('text-xs'); break;
    case 'sm': classes.push('text-sm'); break;
    case 'base': classes.push('text-base'); break;
    case 'lg': classes.push('text-lg'); break;
    case 'xl': classes.push('text-xl'); break;
  }
  
  // 字体粗细
  switch (style.fontWeight) {
    case 'normal': classes.push('font-normal'); break;
    case 'medium': classes.push('font-medium'); break;
    case 'semibold': classes.push('font-semibold'); break;
    case 'bold': classes.push('font-bold'); break;
  }
  
  // 颜色
  switch (style.color) {
    case 'default': classes.push('text-foreground'); break;
    case 'muted': classes.push('text-muted-foreground'); break;
    case 'primary': classes.push('text-primary'); break;
    case 'secondary': classes.push('text-secondary'); break;
    case 'destructive': classes.push('text-destructive'); break;
    case 'warning': classes.push('text-warning'); break;
    case 'success': classes.push('text-success'); break;
  }
  
  // 对齐方式
  switch (style.textAlign) {
    case 'left': classes.push('text-left'); break;
    case 'center': classes.push('text-center'); break;
    case 'right': classes.push('text-right'); break;
  }
  
  return classes.join(' ');
};

/**
 * 获取卡片样式类名
 */
const getCardStyleClasses = (config: TaskCardConfig): string => {
  const classes = ['relative overflow-hidden cursor-pointer'];
  
  // 主题
  switch (config.style.theme) {
    case 'modern':
      classes.push('bg-gradient-to-br from-slate-50 to-slate-100 border-slate-200');
      break;
    case 'glass':
      classes.push('bg-white/80 backdrop-blur-sm border-white/20');
      break;
    case 'gradient':
      classes.push('bg-gradient-to-br from-blue-50 via-white to-purple-50 border-blue-200');
      break;
    case 'dark':
      classes.push('bg-slate-800 border-slate-700 text-white');
      break;
    default:
      classes.push('bg-white border-gray-200');
  }
  
  // 圆角
  switch (config.style.borderRadius) {
    case 'none': classes.push('rounded-none'); break;
    case 'sm': classes.push('rounded-sm'); break;
    case 'md': classes.push('rounded-md'); break;
    case 'lg': classes.push('rounded-lg'); break;
    case 'xl': classes.push('rounded-xl'); break;
  }
  
  // 阴影
  switch (config.style.shadow) {
    case 'none': classes.push('shadow-none'); break;
    case 'sm': classes.push('shadow-sm'); break;
    case 'md': classes.push('shadow-md'); break;
    case 'lg': classes.push('shadow-lg'); break;
    case 'xl': classes.push('shadow-xl'); break;
  }
  
  // 动画
  switch (config.style.animation) {
    case 'subtle':
      classes.push('transition-all duration-200 hover:shadow-md');
      break;
    case 'smooth':
      classes.push('transition-all duration-300 hover:scale-[1.02] hover:shadow-lg');
      break;
    case 'bouncy':
      classes.push('transition-all duration-300 hover:scale-105 hover:shadow-xl hover:rotate-1');
      break;
    default:
      classes.push('transition-all duration-150 ease-out');
  }
  
  return classes.join(' ');
};

/**
 * 获取间距类名
 */
const getSpacingClasses = (config: TaskCardConfig): string => {
  switch (config.style.spacing) {
    case 'tight': return 'p-1 space-y-1';
    case 'loose': return 'p-4 space-y-4';
    default: return 'p-2 space-y-2';
  }
};

/**
 * 可配置任务卡片组件
 */
export const ConfigurableTaskCard: React.FC<ConfigurableTaskCardProps> = ({
  task,
  vehicles,
  config,
  size = 'medium',
  className,
  vehicleDisplayMode = 'compact' as VehicleDisplayMode,
  inTaskVehicleCardStyles = {
    cardWidth: 'w-14',
    cardHeight: 'h-8',
    fontSize: 'text-[10px]',
    fontColor: 'text-foreground',
    vehicleNumberFontWeight: 'font-medium',
    cardBgColor: 'bg-background',
    statusDotSize: 'w-1 h-1',
    borderRadius: 'rounded-sm',
    boxShadow: 'shadow-sm'
  },
  density = 'normal',
  onTaskContextMenu,
  onTaskDoubleClick,
  onDropVehicleOnLine,
  onCancelDispatch,
  onOpenStyleEditor,
  onOpenDeliveryOrderDetails,
  onOpenVehicleContextMenu
}) => {
  const [showProductionPanel, setShowProductionPanel] = React.useState(false);

  const statusInfo = getStatusInfo(task.dispatchStatus);
  const StatusIcon = statusInfo.icon;

  // 根据尺寸获取卡片宽度
  const getCardWidth = (size: string) => {
    switch (size) {
      case 'small': return 'w-[280px]';
      case 'medium': return 'w-[350px]';
      case 'large': return 'w-[420px]';
      case 'extra-large': return 'w-[500px]';
      default: return 'w-[350px]';
    }
  };
  
  // 计算进度百分比
  const progressPercentage = useMemo(() => {
    if (task.requiredVolume === 0) return 0;
    return Math.round((task.completedVolume / task.requiredVolume) * 100);
  }, [task.completedVolume, task.requiredVolume]);

  // 计算发车提醒信息
  const dispatchReminderInfo = useMemo(() => {
    if (task.dispatchStatus !== 'InProgress') return null;
    if (!task.dispatchFrequencyMinutes || task.dispatchFrequencyMinutes <= 0) return null;
    if (!task.nextScheduledDispatchTime) return null;

    const now = new Date();
    const nextDispatchTime = new Date(task.nextScheduledDispatchTime);
    const totalSecondsDiff = differenceInSeconds(nextDispatchTime, now);

    if (totalSecondsDiff <= 0) {
      return { text: '立即发车', type: 'urgent' as const, icon: AlertTriangle };
    }

    const minutesDiff = Math.floor(totalSecondsDiff / 60);
    const secondsDiff = totalSecondsDiff % 60;

    if (minutesDiff <= 5) {
      return {
        text: `${minutesDiff}:${secondsDiff.toString().padStart(2, '0')}`,
        type: 'urgent' as const,
        icon: AlertTriangle
      };
    } else if (minutesDiff <= 15) {
      return { text: `距发:${minutesDiff}分钟`, type: 'warning' as const, icon: Clock };
    } else {
      return { text: `距发:${minutesDiff}分钟`, type: 'normal' as const, icon: Clock };
    }
  }, [task.dispatchStatus, task.dispatchFrequencyMinutes, task.nextScheduledDispatchTime]);

  // 拖拽目标设置
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: ItemTypes.VEHICLE_CARD_DISPATCH,
    drop: (item: any, monitor) => {
      if (monitor.didDrop()) return;

      // 处理车辆拖拽到任务卡片
      if (item.vehicle && onDropVehicleOnLine) {
        // 默认分配到第一条生产线
        onDropVehicleOnLine(item.vehicle, task.id, 'L1');
      }
    },
    canDrop: () => task.dispatchStatus === 'InProgress',
    collect: (monitor) => ({
      isOver: monitor.isOver({ shallow: true }),
      canDrop: monitor.canDrop(),
    }),
  });

  // 当有拖拽悬停时显示生产线面板
  React.useEffect(() => {
    if (isOver && canDrop) {
      setShowProductionPanel(true);
    } else if (!isOver) {
      const timer = setTimeout(() => {
        setShowProductionPanel(false);
      }, 150);
      return () => clearTimeout(timer);
    }
  }, [isOver, canDrop]);

  // 事件处理
  const handleTaskContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    onTaskContextMenu?.(e, task);
  };

  const handleTaskDoubleClick = () => {
    onTaskDoubleClick?.(task);
  };

  // 缓存拖拽样式
  const dragStyles = React.useMemo(() => {
    if (isOver && canDrop) {
      return 'ring-2 ring-primary shadow-lg bg-primary/5';
    }
    return '';
  }, [isOver, canDrop]);

  return (
    <div className="relative z-0" ref={drop as any}>
      <Card
        className={cn(
          getCardStyleClasses(config),
          'min-h-[380px] flex flex-col', // 最小高度，自适应内容
          getCardWidth(size), // 根据尺寸设置宽度
          dragStyles,
          className
        )}
        onContextMenu={handleTaskContextMenu}
        onDoubleClick={handleTaskDoubleClick}
      >
        <CardContent className={cn('flex-1 flex flex-col', getSpacingClasses(config))}>
          {/* 顶部区域 */}
          {config.areas.top.visible && (
            <div className="flex items-center justify-between border-b border-border/50">
              {/* 左侧：环形进度 + 项目信息 */}
              <div className="flex items-center gap-3 min-w-0 flex-1">
                <CircularProgress 
                  percentage={progressPercentage} 
                  size="small" 
                  style={config.areas.top.fields.progressRing}
                />
                <div className="min-w-0 flex-1">
                  {config.areas.top.fields.projectName.visible && (
                    <div className={cn('truncate', getFieldStyleClasses(config.areas.top.fields.projectName))}>
                      {task.projectName || task.taskNumber}
                    </div>
                  )}
                  {config.areas.top.fields.constructionSite.visible && (
                    <div className={cn('truncate', getFieldStyleClasses(config.areas.top.fields.constructionSite))}>
                      {task.constructionSite}
                    </div>
                  )}
                </div>
              </div>
              
              {/* 右侧：发车提醒 + 强度 */}
              <div className="flex flex-col items-end gap-1 flex-shrink-0">
                {config.areas.top.fields.dispatchReminder.visible && dispatchReminderInfo && (
                  <Badge 
                    className={cn(
                      'border-2 cursor-help',
                      getFieldStyleClasses(config.areas.top.fields.dispatchReminder),
                      dispatchReminderInfo.type === 'urgent' 
                        ? 'bg-destructive/20 text-destructive border-destructive animate-pulse shadow-lg'
                        : dispatchReminderInfo.type === 'warning'
                        ? 'bg-warning/20 text-warning border-warning shadow-md'
                        : 'bg-muted/20 text-muted-foreground border-muted'
                    )}
                    title={`下次发车时间: ${task.nextScheduledDispatchTime ? new Date(task.nextScheduledDispatchTime).toLocaleString() : '未计划'}`}
                  >
                    {React.createElement(dispatchReminderInfo.icon, { className: "w-3 h-3 mr-1" })}
                    {dispatchReminderInfo.text}
                  </Badge>
                )}
                {config.areas.top.fields.strength.visible && (
                  <Badge className={cn('bg-orange-100 text-orange-700 border-orange-200', getFieldStyleClasses(config.areas.top.fields.strength))}>
                    <Target className="w-3 h-3 mr-1" />
                    {task.strength}
                  </Badge>
                )}
              </div>
            </div>
          )}

          {/* 调度车辆区域 - 不可隐藏，固定高度 */}
          <div className="border-b border-border/50 pb-3">
            <div className="flex items-center gap-2 mb-3">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                  <Truck className="w-4 h-4 text-blue-600" />
                </div>
                {config.areas.vehicle.fields.vehicleCount.visible && (
                  <div className="flex flex-col">
                    <span className={cn('font-medium', getFieldStyleClasses(config.areas.vehicle.fields.vehicleCount))}>
                      调度车辆
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {vehicles.length} 辆车
                    </span>
                  </div>
                )}
              </div>
            </div>
            <div className="h-[90px] bg-gradient-to-br from-slate-50 to-slate-100 border-2 border-dashed border-slate-300 rounded-xl p-3 overflow-hidden transition-all duration-200 hover:border-blue-300 hover:bg-gradient-to-br hover:from-blue-50 hover:to-slate-50">
              {vehicles.length > 0 ? (
                <div className="h-full overflow-x-auto overflow-y-hidden">
                  <div className="flex flex-wrap gap-2 h-full content-start" style={{ minWidth: 'max-content' }}>
                    {vehicles.map((vehicle) => (
                      <InTaskVehicleCard
                        key={vehicle.id}
                        vehicle={vehicle}
                        task={task}
                        vehicleDisplayMode={vehicleDisplayMode}
                        inTaskVehicleCardStyles={inTaskVehicleCardStyles}
                        productionLineCount={task.productionLineCount || 3}
                        density={density}
                        onCancelDispatch={onCancelDispatch}
                        onOpenStyleEditor={onOpenStyleEditor}
                        onOpenDeliveryOrderDetails={(vehicleId) => onOpenDeliveryOrderDetails?.(vehicleId, task.id)}
                        onOpenContextMenu={(e, vehicle) => onOpenVehicleContextMenu?.(e, vehicle, task)}
                      />
                    ))}
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-full text-center">
                  <div className="w-10 h-10 rounded-full bg-slate-200 flex items-center justify-center mb-2">
                    <Truck className="w-5 h-5 text-slate-400" />
                  </div>
                  <span className="text-xs text-slate-500 font-medium">拖拽车辆到此区域</span>
                  <span className="text-xs text-slate-400">完成车辆调度</span>
                </div>
              )}
            </div>
          </div>

          {/* 内容区域 - 自适应高度 */}
          {config.areas.content.visible && (
            <div className="flex-1 border-b border-border/50 py-4">
              <div className={cn(
                'grid gap-6',
                config.areas.content.layout === 'double' ? 'grid-cols-2' : 'grid-cols-1'
              )}>
                {/* 左列或单列 */}
                <div className="space-y-4">
                  {config.areas.content.fields.requiredVolume.visible && (
                    <div className="bg-gradient-to-r from-blue-50 to-transparent p-3 rounded-lg border-l-4 border-blue-400">
                      <div className="text-xs text-blue-600 font-medium mb-1">需求方量</div>
                      <div className={cn('text-lg font-bold text-blue-800', getFieldStyleClasses(config.areas.content.fields.requiredVolume))}>
                        {task.requiredVolume}m³
                      </div>
                    </div>
                  )}
                  {config.areas.content.fields.completedVolume.visible && (
                    <div className="bg-gradient-to-r from-green-50 to-transparent p-3 rounded-lg border-l-4 border-green-400">
                      <div className="text-xs text-green-600 font-medium mb-1">完成方量</div>
                      <div className={cn('text-lg font-bold text-green-800', getFieldStyleClasses(config.areas.content.fields.completedVolume))}>
                        {task.completedVolume}m³
                      </div>
                    </div>
                  )}
                  {config.areas.content.fields.scheduledTime.visible && (
                    <div className="flex items-center gap-3 p-2 rounded-lg bg-slate-50">
                      <Clock className="w-4 h-4 text-slate-500" />
                      <div>
                        <div className="text-xs text-slate-500 font-medium">计划时间</div>
                        <div className={cn('text-sm font-medium', getFieldStyleClasses(config.areas.content.fields.scheduledTime))}>
                          {task.scheduledTime || '--'}
                        </div>
                      </div>
                    </div>
                  )}
                  {config.areas.content.fields.contactPhone.visible && (
                    <div className="flex items-center gap-3 p-2 rounded-lg bg-slate-50">
                      <div className="w-4 h-4 rounded-full bg-slate-300 flex items-center justify-center">
                        <span className="text-xs text-slate-600">📞</span>
                      </div>
                      <div>
                        <div className="text-xs text-slate-500 font-medium">联系电话</div>
                        <div className={cn('text-sm font-medium', getFieldStyleClasses(config.areas.content.fields.contactPhone))}>
                          {task.contactPhone || '--'}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                
                {/* 右列（仅在双列布局时显示） */}
                {config.areas.content.layout === 'double' && (
                  <div className="space-y-4">
                    {config.areas.content.fields.completedProgress.visible && (
                      <div className="bg-gradient-to-r from-purple-50 to-transparent p-3 rounded-lg border-l-4 border-purple-400">
                        <div className="text-xs text-purple-600 font-medium mb-1">完成进度</div>
                        <div className={cn('text-lg font-bold text-purple-800', getFieldStyleClasses(config.areas.content.fields.completedProgress))}>
                          {progressPercentage}%
                        </div>
                      </div>
                    )}
                    {config.areas.content.fields.estimatedDuration.visible && (
                      <div className="flex items-center gap-3 p-2 rounded-lg bg-slate-50">
                        <Clock className="w-4 h-4 text-slate-500" />
                        <div>
                          <div className="text-xs text-slate-500 font-medium">预计时长</div>
                          <div className={cn('text-sm font-medium', getFieldStyleClasses(config.areas.content.fields.estimatedDuration))}>
                            --
                          </div>
                        </div>
                      </div>
                    )}
                    {config.areas.content.fields.constructionLocation.visible && (
                      <div className="flex items-center gap-3 p-2 rounded-lg bg-slate-50">
                        <MapPin className="w-4 h-4 text-slate-500" />
                        <div className="min-w-0 flex-1">
                          <div className="text-xs text-slate-500 font-medium">施工地点</div>
                          <div className={cn('text-sm font-medium truncate', getFieldStyleClasses(config.areas.content.fields.constructionLocation))}>
                            {task.constructionSite}
                          </div>
                        </div>
                      </div>
                    )}
                    {config.areas.content.fields.taskStatus.visible && (
                      <div className="flex items-center gap-3 p-2 rounded-lg bg-slate-50">
                        <div className="w-4 h-4 rounded-full bg-slate-300 flex items-center justify-center">
                          <StatusIcon className="w-2 h-2 text-slate-600" />
                        </div>
                        <div>
                          <div className="text-xs text-slate-500 font-medium">状态</div>
                          <Badge className={cn('text-xs', getFieldStyleClasses(config.areas.content.fields.taskStatus), statusInfo.className)}>
                            <StatusIcon className="w-3 h-3 mr-1" />
                            {statusInfo.label}
                          </Badge>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 底部区域 - 紧凑布局 */}
          {config.areas.bottom.visible && (
            <div className="pt-3 border-t border-border/30 bg-slate-50/50 rounded-b-lg -mx-2 -mb-2 px-2 pb-2">
              <div className={cn(
                'grid gap-3',
                config.areas.bottom.layout === 'double' ? 'grid-cols-2' : 'grid-cols-1'
              )}>
                {/* 左列或单列 */}
                <div className="space-y-2">
                  {config.areas.bottom.fields.customerName.visible && (
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-blue-400"></div>
                      <div className="min-w-0 flex-1">
                        <div className="text-xs text-slate-500 font-medium">客户名称</div>
                        <div className={cn('text-sm font-medium truncate', getFieldStyleClasses(config.areas.bottom.fields.customerName))}>
                          {task.customerName}
                        </div>
                      </div>
                    </div>
                  )}
                  {config.areas.bottom.fields.createdAt.visible && (
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-green-400"></div>
                      <div className="min-w-0 flex-1">
                        <div className="text-xs text-slate-500 font-medium">创建时间</div>
                        <div className={cn('text-sm font-medium', getFieldStyleClasses(config.areas.bottom.fields.createdAt))}>
                          {task.createdAt || '--'}
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* 右列（仅在双列布局时显示） */}
                {config.areas.bottom.layout === 'double' && (
                  <div className="space-y-2">
                    {config.areas.bottom.fields.taskNumber.visible && (
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-purple-400"></div>
                        <div className="min-w-0 flex-1">
                          <div className="text-xs text-slate-500 font-medium">任务编号</div>
                          <div className={cn('text-sm font-medium truncate', getFieldStyleClasses(config.areas.bottom.fields.taskNumber))}>
                            {task.taskNumber}
                          </div>
                        </div>
                      </div>
                    )}
                    {config.areas.bottom.fields.updatedAt.visible && (
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-orange-400"></div>
                        <div className="min-w-0 flex-1">
                          <div className="text-xs text-slate-500 font-medium">更新时间</div>
                          <div className={cn('text-sm font-medium', getFieldStyleClasses(config.areas.bottom.fields.updatedAt))}>
                            {task.createdAt || '--'}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 生产线面板 - 半透明背景遮罩 */}
      {showProductionPanel && (
        <div
          className="absolute inset-0 bg-black/20 backdrop-blur-sm rounded-lg"
          style={{ zIndex: 5 }}
        />
      )}

      {/* 生产线面板 */}
      <div
        className={cn(
          'absolute top-0 right-0 h-full w-56 bg-white/95 backdrop-blur-md',
          'border-l-2 border-blue-200 shadow-2xl rounded-r-lg',
          'transform transition-all duration-500 ease-in-out',
          'translate-x-full opacity-0',
          showProductionPanel && 'translate-x-0 opacity-100'
        )}
        style={{ zIndex: 10 }}
      >
        <div className="p-5 h-full flex flex-col">
          {/* 头部 */}
          <div className="flex items-center gap-3 mb-6 pb-3 border-b-2 border-blue-100">
            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-lg">
              <Factory className="w-5 h-5 text-white" />
            </div>
            <div>
              <span className="font-bold text-lg text-gray-800">选择生产线</span>
              <div className="text-xs text-gray-500">拖拽车辆到生产线</div>
            </div>
          </div>

          {/* 生产线列表 */}
          <div className="flex-1 space-y-4 overflow-y-auto">
            {Array.from({ length: task.productionLineCount || 3 }).map((_, index) => {
              const lineId = `L${index + 1}`;
              return (
                <ProductionLineDropZone
                  key={index}
                  lineId={lineId}
                  lineNumber={index + 1}
                  taskId={task.id}
                  onDropVehicle={onDropVehicleOnLine}
                />
              );
            })}
          </div>

          {/* 底部提示 */}
          <div className="mt-6 pt-4 border-t-2 border-blue-100">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <div className="w-2 h-2 rounded-full bg-blue-400 animate-pulse"></div>
              <span>拖拽车辆到生产线完成发车</span>
            </div>
            <div className="text-xs text-gray-400 mt-1">
              任务: {task.taskNumber}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
