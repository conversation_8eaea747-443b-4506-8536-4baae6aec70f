'use client';

import React, { useMemo } from 'react';
import { useDrop } from 'react-dnd';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import {
  Clock,
  Truck,
  AlertTriangle,
  CheckCircle2,
  Pause,
  XCircle,
  Target,
  Factory
} from 'lucide-react';
import { differenceInSeconds } from 'date-fns';
import type { Task, Vehicle, VehicleDisplayMode, InTaskVehicleCardStyle } from '@/types';
import { ItemTypes } from '@/constants/dndItemTypes';
import { InTaskVehicleCard } from '../in-task-vehicle-card';
import { TaskCardConfig, FieldStyle } from '@/types/taskCardConfig';

/**
 * 可配置任务卡片组件属性
 */
interface ConfigurableTaskCardProps {
  task: Task;
  vehicles: Vehicle[];
  config: TaskCardConfig;
  className?: string;
  vehicleDisplayMode?: VehicleDisplayMode;
  inTaskVehicleCardStyles?: InTaskVehicleCardStyle;
  density?: 'compact' | 'normal' | 'loose';
  onTaskContextMenu?: (event: React.MouseEvent, task: Task) => void;
  onTaskDoubleClick?: (task: Task) => void;
  onDropVehicleOnLine?: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  onCancelDispatch?: (vehicleId: string) => void;
  onOpenStyleEditor?: () => void;
  onOpenDeliveryOrderDetails?: (vehicleId: string, taskId: string) => void;
  onOpenVehicleContextMenu?: (event: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
}

/**
 * 获取状态图标和样式
 */
const getStatusInfo = (status: string) => {
  switch (status) {
    case 'ReadyToProduce':
      return { icon: Pause, label: '待生产', className: 'bg-yellow-100 text-yellow-700 border-yellow-200' };
    case 'InProgress':
      return { icon: Clock, label: '进行中', className: 'bg-blue-100 text-blue-700 border-blue-200' };
    case 'Completed':
      return { icon: CheckCircle2, label: '已完成', className: 'bg-green-100 text-green-700 border-green-200' };
    case 'Cancelled':
      return { icon: XCircle, label: '已取消', className: 'bg-red-100 text-red-700 border-red-200' };
    default:
      return { icon: AlertTriangle, label: '未知', className: 'bg-gray-100 text-gray-700 border-gray-200' };
  }
};

/**
 * 环形进度组件
 */
const CircularProgress: React.FC<{ percentage: number; size?: 'small' | 'medium'; style?: FieldStyle }> = ({ 
  percentage, 
  size = 'small',
  style
}) => {
  const sizeClass = size === 'small' ? 'w-8 h-8' : 'w-12 h-12';
  const strokeWidth = size === 'small' ? 2 : 3;
  const radius = size === 'small' ? 14 : 20;
  const circumference = 2 * Math.PI * radius;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  return (
    <div className={cn('relative', sizeClass)}>
      <svg className="transform -rotate-90 w-full h-full">
        <circle
          cx="50%"
          cy="50%"
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="transparent"
          className="text-gray-200"
        />
        <circle
          cx="50%"
          cy="50%"
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          className="text-blue-500 transition-all duration-300"
        />
      </svg>
      <div className="absolute inset-0 flex items-center justify-center">
        <span className={cn(
          'font-medium text-gray-700',
          size === 'small' ? 'text-xs' : 'text-sm'
        )}>
          {percentage}%
        </span>
      </div>
    </div>
  );
};

/**
 * 获取字段样式类名
 */
const getFieldStyleClasses = (style: FieldStyle): string => {
  const classes = [];
  
  // 字体大小
  switch (style.fontSize) {
    case 'xs': classes.push('text-xs'); break;
    case 'sm': classes.push('text-sm'); break;
    case 'base': classes.push('text-base'); break;
    case 'lg': classes.push('text-lg'); break;
    case 'xl': classes.push('text-xl'); break;
  }
  
  // 字体粗细
  switch (style.fontWeight) {
    case 'normal': classes.push('font-normal'); break;
    case 'medium': classes.push('font-medium'); break;
    case 'semibold': classes.push('font-semibold'); break;
    case 'bold': classes.push('font-bold'); break;
  }
  
  // 颜色
  switch (style.color) {
    case 'default': classes.push('text-foreground'); break;
    case 'muted': classes.push('text-muted-foreground'); break;
    case 'primary': classes.push('text-primary'); break;
    case 'secondary': classes.push('text-secondary'); break;
    case 'destructive': classes.push('text-destructive'); break;
    case 'warning': classes.push('text-warning'); break;
    case 'success': classes.push('text-success'); break;
  }
  
  // 对齐方式
  switch (style.textAlign) {
    case 'left': classes.push('text-left'); break;
    case 'center': classes.push('text-center'); break;
    case 'right': classes.push('text-right'); break;
  }
  
  return classes.join(' ');
};

/**
 * 获取卡片样式类名
 */
const getCardStyleClasses = (config: TaskCardConfig): string => {
  const classes = ['relative overflow-hidden cursor-pointer'];
  
  // 主题
  switch (config.style.theme) {
    case 'modern':
      classes.push('bg-gradient-to-br from-slate-50 to-slate-100 border-slate-200');
      break;
    case 'glass':
      classes.push('bg-white/80 backdrop-blur-sm border-white/20');
      break;
    case 'gradient':
      classes.push('bg-gradient-to-br from-blue-50 via-white to-purple-50 border-blue-200');
      break;
    case 'dark':
      classes.push('bg-slate-800 border-slate-700 text-white');
      break;
    default:
      classes.push('bg-white border-gray-200');
  }
  
  // 圆角
  switch (config.style.borderRadius) {
    case 'none': classes.push('rounded-none'); break;
    case 'sm': classes.push('rounded-sm'); break;
    case 'md': classes.push('rounded-md'); break;
    case 'lg': classes.push('rounded-lg'); break;
    case 'xl': classes.push('rounded-xl'); break;
  }
  
  // 阴影
  switch (config.style.shadow) {
    case 'none': classes.push('shadow-none'); break;
    case 'sm': classes.push('shadow-sm'); break;
    case 'md': classes.push('shadow-md'); break;
    case 'lg': classes.push('shadow-lg'); break;
    case 'xl': classes.push('shadow-xl'); break;
  }
  
  // 动画
  switch (config.style.animation) {
    case 'subtle':
      classes.push('transition-all duration-200 hover:shadow-md');
      break;
    case 'smooth':
      classes.push('transition-all duration-300 hover:scale-[1.02] hover:shadow-lg');
      break;
    case 'bouncy':
      classes.push('transition-all duration-300 hover:scale-105 hover:shadow-xl hover:rotate-1');
      break;
    default:
      classes.push('transition-all duration-150 ease-out');
  }
  
  return classes.join(' ');
};

/**
 * 获取间距类名
 */
const getSpacingClasses = (config: TaskCardConfig): string => {
  switch (config.style.spacing) {
    case 'tight': return 'p-1 space-y-1';
    case 'loose': return 'p-4 space-y-4';
    default: return 'p-2 space-y-2';
  }
};

/**
 * 可配置任务卡片组件
 */
export const ConfigurableTaskCard: React.FC<ConfigurableTaskCardProps> = ({
  task,
  vehicles,
  config,
  className,
  vehicleDisplayMode = 'compact' as VehicleDisplayMode,
  inTaskVehicleCardStyles = {
    cardWidth: 'w-14',
    cardHeight: 'h-8',
    fontSize: 'text-[10px]',
    fontColor: 'text-foreground',
    vehicleNumberFontWeight: 'font-medium',
    cardBgColor: 'bg-background',
    statusDotSize: 'w-1 h-1',
    borderRadius: 'rounded-sm',
    boxShadow: 'shadow-sm'
  },
  density = 'normal',
  onTaskContextMenu,
  onTaskDoubleClick,
  onDropVehicleOnLine,
  onCancelDispatch,
  onOpenStyleEditor,
  onOpenDeliveryOrderDetails,
  onOpenVehicleContextMenu
}) => {
  const [showProductionPanel, setShowProductionPanel] = React.useState(false);
  
  const statusInfo = getStatusInfo(task.dispatchStatus);
  const StatusIcon = statusInfo.icon;
  
  // 计算进度百分比
  const progressPercentage = useMemo(() => {
    if (task.requiredVolume === 0) return 0;
    return Math.round((task.completedVolume / task.requiredVolume) * 100);
  }, [task.completedVolume, task.requiredVolume]);

  // 计算发车提醒信息
  const dispatchReminderInfo = useMemo(() => {
    if (task.dispatchStatus !== 'InProgress') return null;
    if (!task.dispatchFrequencyMinutes || task.dispatchFrequencyMinutes <= 0) return null;
    if (!task.nextScheduledDispatchTime) return null;

    const now = new Date();
    const nextDispatchTime = new Date(task.nextScheduledDispatchTime);
    const totalSecondsDiff = differenceInSeconds(nextDispatchTime, now);

    if (totalSecondsDiff <= 0) {
      return { text: '立即发车', type: 'urgent' as const, icon: AlertTriangle };
    }

    const minutesDiff = Math.floor(totalSecondsDiff / 60);
    const secondsDiff = totalSecondsDiff % 60;

    if (minutesDiff <= 5) {
      return {
        text: `${minutesDiff}:${secondsDiff.toString().padStart(2, '0')}`,
        type: 'urgent' as const,
        icon: AlertTriangle
      };
    } else if (minutesDiff <= 15) {
      return { text: `距发:${minutesDiff}分钟`, type: 'warning' as const, icon: Clock };
    } else {
      return { text: `距发:${minutesDiff}分钟`, type: 'normal' as const, icon: Clock };
    }
  }, [task.dispatchStatus, task.dispatchFrequencyMinutes, task.nextScheduledDispatchTime]);

  // 拖拽目标设置
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: ItemTypes.VEHICLE_CARD_DISPATCH,
    drop: (item: any, monitor) => {
      if (monitor.didDrop()) return;
    },
    canDrop: () => task.dispatchStatus === 'InProgress',
    collect: (monitor) => ({
      isOver: monitor.isOver({ shallow: true }),
      canDrop: monitor.canDrop(),
    }),
  });

  // 当有拖拽悬停时显示生产线面板
  React.useEffect(() => {
    if (isOver && canDrop) {
      setShowProductionPanel(true);
    } else if (!isOver) {
      const timer = setTimeout(() => {
        setShowProductionPanel(false);
      }, 150);
      return () => clearTimeout(timer);
    }
  }, [isOver, canDrop]);

  // 事件处理
  const handleTaskContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    onTaskContextMenu?.(e, task);
  };

  const handleTaskDoubleClick = () => {
    onTaskDoubleClick?.(task);
  };

  // 缓存拖拽样式
  const dragStyles = React.useMemo(() => {
    if (isOver && canDrop) {
      return 'ring-2 ring-primary shadow-lg bg-primary/5';
    }
    return '';
  }, [isOver, canDrop]);

  return (
    <div className="relative z-0" ref={drop as any}>
      <Card 
        className={cn(
          getCardStyleClasses(config),
          'h-[420px] w-[350px]', // 固定高度和宽度
          dragStyles,
          className
        )}
        onContextMenu={handleTaskContextMenu}
        onDoubleClick={handleTaskDoubleClick}
      >
        <CardContent className={cn('h-full flex flex-col', getSpacingClasses(config))}>
          {/* 顶部区域 */}
          {config.areas.top.visible && (
            <div className="flex items-center justify-between border-b border-border/50">
              {/* 左侧：环形进度 + 项目信息 */}
              <div className="flex items-center gap-3 min-w-0 flex-1">
                <CircularProgress 
                  percentage={progressPercentage} 
                  size="small" 
                  style={config.areas.top.fields.progressRing}
                />
                <div className="min-w-0 flex-1">
                  {config.areas.top.fields.projectName.visible && (
                    <div className={cn('truncate', getFieldStyleClasses(config.areas.top.fields.projectName))}>
                      {task.projectName || task.taskNumber}
                    </div>
                  )}
                  {config.areas.top.fields.constructionSite.visible && (
                    <div className={cn('truncate', getFieldStyleClasses(config.areas.top.fields.constructionSite))}>
                      {task.constructionSite}
                    </div>
                  )}
                </div>
              </div>
              
              {/* 右侧：发车提醒 + 强度 */}
              <div className="flex flex-col items-end gap-1 flex-shrink-0">
                {config.areas.top.fields.dispatchReminder.visible && dispatchReminderInfo && (
                  <Badge 
                    className={cn(
                      'border-2 cursor-help',
                      getFieldStyleClasses(config.areas.top.fields.dispatchReminder),
                      dispatchReminderInfo.type === 'urgent' 
                        ? 'bg-destructive/20 text-destructive border-destructive animate-pulse shadow-lg'
                        : dispatchReminderInfo.type === 'warning'
                        ? 'bg-warning/20 text-warning border-warning shadow-md'
                        : 'bg-muted/20 text-muted-foreground border-muted'
                    )}
                    title={`下次发车时间: ${task.nextScheduledDispatchTime ? new Date(task.nextScheduledDispatchTime).toLocaleString() : '未计划'}`}
                  >
                    {React.createElement(dispatchReminderInfo.icon, { className: "w-3 h-3 mr-1" })}
                    {dispatchReminderInfo.text}
                  </Badge>
                )}
                {config.areas.top.fields.strength.visible && (
                  <Badge className={cn('bg-orange-100 text-orange-700 border-orange-200', getFieldStyleClasses(config.areas.top.fields.strength))}>
                    <Target className="w-3 h-3 mr-1" />
                    {task.strength}
                  </Badge>
                )}
              </div>
            </div>
          )}

          {/* 调度车辆区域 - 不可隐藏 */}
          <div className="border-b border-border/50">
            <div className="flex items-center gap-2 mb-2">
              <Truck className="w-4 h-4 text-muted-foreground" />
              {config.areas.vehicle.fields.vehicleCount.visible && (
                <span className={getFieldStyleClasses(config.areas.vehicle.fields.vehicleCount)}>
                  调度车辆 ({vehicles.length})
                </span>
              )}
            </div>
            <div className="h-[80px] bg-muted/30 border border-dashed border-muted-foreground/30 rounded-lg p-2 overflow-hidden">
              {vehicles.length > 0 ? (
                <div className="h-full overflow-x-auto overflow-y-hidden">
                  <div className="flex flex-wrap gap-1 h-full content-start" style={{ minWidth: 'max-content' }}>
                    {vehicles.map((vehicle) => (
                      <InTaskVehicleCard
                        key={vehicle.id}
                        vehicle={vehicle}
                        task={task}
                        vehicleDisplayMode={vehicleDisplayMode}
                        inTaskVehicleCardStyles={inTaskVehicleCardStyles}
                        productionLineCount={task.productionLineCount || 3}
                        density={density}
                        onCancelDispatch={onCancelDispatch}
                        onOpenStyleEditor={onOpenStyleEditor}
                        onOpenDeliveryOrderDetails={(vehicleId) => onOpenDeliveryOrderDetails?.(vehicleId, task.id)}
                        onOpenContextMenu={(e, vehicle) => onOpenVehicleContextMenu?.(e, vehicle, task)}
                      />
                    ))}
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-full text-xs text-muted-foreground">
                  可拖拽车辆到此区域
                </div>
              )}
            </div>
          </div>

          {/* 内容区域 */}
          {config.areas.content.visible && (
            <div className="flex-1 border-b border-border/50">
              <div className={cn(
                'grid gap-4 h-full',
                config.areas.content.layout === 'double' ? 'grid-cols-2' : 'grid-cols-1'
              )}>
                {/* 左列或单列 */}
                <div className="space-y-3">
                  {config.areas.content.fields.requiredVolume.visible && (
                    <div className="text-xs">
                      <div className="text-muted-foreground">需求方量</div>
                      <div className={getFieldStyleClasses(config.areas.content.fields.requiredVolume)}>
                        {task.requiredVolume}m³
                      </div>
                    </div>
                  )}
                  {config.areas.content.fields.completedVolume.visible && (
                    <div className="text-xs">
                      <div className="text-muted-foreground">完成方量</div>
                      <div className={getFieldStyleClasses(config.areas.content.fields.completedVolume)}>
                        {task.completedVolume}m³
                      </div>
                    </div>
                  )}
                  {config.areas.content.fields.scheduledTime.visible && (
                    <div className="text-xs">
                      <div className="text-muted-foreground">计划时间</div>
                      <div className={getFieldStyleClasses(config.areas.content.fields.scheduledTime)}>
                        {task.scheduledTime || '--'}
                      </div>
                    </div>
                  )}
                  {config.areas.content.fields.contactPhone.visible && (
                    <div className="text-xs">
                      <div className="text-muted-foreground">联系电话</div>
                      <div className={getFieldStyleClasses(config.areas.content.fields.contactPhone)}>
                        {task.contactPhone || '--'}
                      </div>
                    </div>
                  )}
                </div>
                
                {/* 右列（仅在双列布局时显示） */}
                {config.areas.content.layout === 'double' && (
                  <div className="space-y-3">
                    {config.areas.content.fields.completedProgress.visible && (
                      <div className="text-xs">
                        <div className="text-muted-foreground">完成进度</div>
                        <div className={getFieldStyleClasses(config.areas.content.fields.completedProgress)}>
                          {progressPercentage}%
                        </div>
                      </div>
                    )}
                    {config.areas.content.fields.estimatedDuration.visible && (
                      <div className="text-xs">
                        <div className="text-muted-foreground">预计时长</div>
                        <div className={getFieldStyleClasses(config.areas.content.fields.estimatedDuration)}>
                          --
                        </div>
                      </div>
                    )}
                    {config.areas.content.fields.constructionLocation.visible && (
                      <div className="text-xs">
                        <div className="text-muted-foreground">施工地点</div>
                        <div className={cn('truncate', getFieldStyleClasses(config.areas.content.fields.constructionLocation))}>
                          {task.constructionSite}
                        </div>
                      </div>
                    )}
                    {config.areas.content.fields.taskStatus.visible && (
                      <div className="text-xs">
                        <div className="text-muted-foreground">状态</div>
                        <Badge className={cn(getFieldStyleClasses(config.areas.content.fields.taskStatus), statusInfo.className)}>
                          <StatusIcon className="w-3 h-3 mr-1" />
                          {statusInfo.label}
                        </Badge>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 底部区域 */}
          {config.areas.bottom.visible && (
            <div>
              <div className={cn(
                'grid gap-4',
                config.areas.bottom.layout === 'double' ? 'grid-cols-2' : 'grid-cols-1'
              )}>
                {/* 左列或单列 */}
                <div className="space-y-2">
                  {config.areas.bottom.fields.customerName.visible && (
                    <div className="text-xs">
                      <div className="text-muted-foreground">客户名称</div>
                      <div className={cn('truncate', getFieldStyleClasses(config.areas.bottom.fields.customerName))}>
                        {task.customerName}
                      </div>
                    </div>
                  )}
                  {config.areas.bottom.fields.createdAt.visible && (
                    <div className="text-xs">
                      <div className="text-muted-foreground">创建时间</div>
                      <div className={getFieldStyleClasses(config.areas.bottom.fields.createdAt)}>
                        {task.createdAt || '--'}
                      </div>
                    </div>
                  )}
                </div>
                
                {/* 右列（仅在双列布局时显示） */}
                {config.areas.bottom.layout === 'double' && (
                  <div className="space-y-2">
                    {config.areas.bottom.fields.taskNumber.visible && (
                      <div className="text-xs">
                        <div className="text-muted-foreground">任务编号</div>
                        <div className={cn('truncate', getFieldStyleClasses(config.areas.bottom.fields.taskNumber))}>
                          {task.taskNumber}
                        </div>
                      </div>
                    )}
                    {config.areas.bottom.fields.updatedAt.visible && (
                      <div className="text-xs">
                        <div className="text-muted-foreground">更新时间</div>
                        <div className={getFieldStyleClasses(config.areas.bottom.fields.updatedAt)}>
                          {task.createdAt || '--'}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 生产线面板 */}
      {showProductionPanel && (
        <div 
          className={cn(
            'absolute top-0 right-0 h-full w-48 bg-background/95 backdrop-blur-sm',
            'border-l border-border shadow-lg rounded-r-lg',
            'transform transition-all duration-300 ease-out',
            'translate-x-full',
            showProductionPanel && 'translate-x-0'
          )}
          style={{ zIndex: 10 }}
        >
          <div className="p-4 h-full flex flex-col">
            <div className="flex items-center gap-2 mb-4 pb-2 border-b">
              <Factory className="w-4 h-4 text-primary" />
              <span className="font-medium text-sm">选择生产线</span>
            </div>
            
            <div className="flex-1 space-y-3 overflow-y-auto">
              {Array.from({ length: task.productionLineCount || 3 }).map((_, index) => (
                <div
                  key={index}
                  className={cn(
                    'w-full h-12 rounded-lg border-2 border-dashed transition-all duration-200',
                    'flex items-center justify-center text-sm font-medium',
                    'border-muted-foreground/30 bg-muted/20 text-muted-foreground',
                    'hover:border-primary/50 hover:bg-primary/5'
                  )}
                >
                  <Factory className="w-4 h-4 mr-2" />
                  生产线 {index + 1}
                </div>
              ))}
            </div>
            
            <div className="mt-4 pt-2 border-t text-xs text-muted-foreground">
              拖拽车辆到生产线完成发车
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
