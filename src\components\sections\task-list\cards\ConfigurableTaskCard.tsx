'use client';

import React, { useMemo, memo, useCallback } from 'react';
import { useDrop } from 'react-dnd';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import {
  Clock,
  Truck,
  AlertTriangle,
  CheckCircle2,
  Pause,
  XCircle,
  Target,
  Factory,
  MessageCircle} from 'lucide-react';
import { differenceInSeconds } from 'date-fns';
import type { Task, Vehicle, VehicleDisplayMode, InTaskVehicleCardStyle } from '@/types';
import { ItemTypes } from '@/constants/dndItemTypes';
import { InTaskVehicleCard } from '../in-task-vehicle-card';
import { TaskCardConfig, FieldStyle } from '@/types/taskCardConfig';
import { useTaskListSettings } from '@/hooks/useTaskListSettings';
import { TaskMessageModal } from './TaskMessageModal';

/**
 * 生产线拖拽区域组件
 */
interface ProductionLineDropZoneProps {
  lineId: string;
  lineNumber: number;
  taskId: string;
  onDropVehicle?: (vehicle: Vehicle, taskId: string, lineId: string) => void;
}

const ProductionLineDropZone: React.FC<ProductionLineDropZoneProps> = ({
  lineId,
  lineNumber,
  taskId,
  onDropVehicle
}) => {
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: ItemTypes.VEHICLE_CARD_DISPATCH,
    drop: (item: any, monitor) => {
      if (monitor.didDrop()) return { dropped: true };

      if (item.vehicle && onDropVehicle) {
        onDropVehicle(item.vehicle, taskId, lineId);
        return { dropped: true };
      }
    },
    canDrop: () => true,
    collect: (monitor) => ({
      isOver: monitor.isOver({ shallow: true }),
      canDrop: monitor.canDrop(),
    }),
  });

  return (
    <div
      ref={drop as any}
      className={cn(
        'w-full h-14 rounded-xl border-2 border-dashed transition-all duration-300',
        'flex items-center justify-between px-4 text-sm font-medium cursor-pointer',
        'transform transition-transform hover:scale-102',
        'relative overflow-hidden',
        isOver && canDrop
          ? 'border-green-400 bg-gradient-to-r from-green-50 to-green-100 text-green-700 scale-105 shadow-xl border-solid'
          : 'border-slate-300 bg-gradient-to-r from-slate-50 to-slate-100 text-slate-600 hover:border-blue-400 hover:from-blue-50 hover:to-blue-100 hover:text-blue-700'
      )}
    >
      {/* 背景动画效果 */}
      {isOver && canDrop && (
        <div className="absolute inset-0 bg-gradient-to-r from-green-200/50 to-green-300/50 animate-pulse" />
      )}

      {/* 内容 */}
      <div className="flex items-center gap-3 relative z-10">
        <div className={cn(
          'w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300',
          isOver && canDrop
            ? 'bg-green-500 shadow-lg scale-110'
            : 'bg-slate-400 hover:bg-blue-500'
        )}>
          <Factory className="w-4 h-4 text-white" />
        </div>
        <span className="font-semibold">生产线 {lineNumber}</span>
      </div>

      {/* 状态指示器 */}
      <div className="flex items-center gap-2 relative z-10">
        {isOver && canDrop ? (
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 rounded-full bg-green-500 animate-bounce" />
            <div className="w-2 h-2 rounded-full bg-green-500 animate-bounce" style={{ animationDelay: '0.1s' }} />
            <div className="w-2 h-2 rounded-full bg-green-500 animate-bounce" style={{ animationDelay: '0.2s' }} />
          </div>
        ) : (
          <div className="w-6 h-6 rounded-full border-2 border-dashed border-current opacity-50" />
        )}
      </div>
    </div>
  );
};

/**
 * 可配置任务卡片组件属性
 */
interface ConfigurableTaskCardProps {
  task: Task;
  vehicles: Vehicle[];
  config: TaskCardConfig;
  size?: 'small'; // 只保留 small 规格
  className?: string;
  vehicleDisplayMode?: VehicleDisplayMode;
  inTaskVehicleCardStyles?: InTaskVehicleCardStyle;
  density?: 'compact' | 'normal' | 'loose';
  onTaskContextMenu?: (event: React.MouseEvent, task: Task) => void;
  onTaskDoubleClick?: (task: Task) => void;
  onDropVehicleOnLine?: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  onCancelDispatch?: (vehicleId: string) => void;
  onOpenStyleEditor?: () => void;
  onOpenDeliveryOrderDetails?: (vehicleId: string, taskId: string) => void;
  onOpenVehicleContextMenu?: (event: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
}

/**
 * 获取状态图标和样式
 */
const getStatusInfo = (status: string) => {
  switch (status) {
    case 'ReadyToProduce':
      return { icon: Pause, label: '待生产', className: 'bg-yellow-100 text-yellow-700 border-yellow-200' };
    case 'InProgress':
      return { icon: Clock, label: '进行中', className: 'bg-blue-100 text-blue-700 border-blue-200' };
    case 'Completed':
      return { icon: CheckCircle2, label: '已完成', className: 'bg-green-100 text-green-700 border-green-200' };
    case 'Cancelled':
      return { icon: XCircle, label: '已取消', className: 'bg-red-100 text-red-700 border-red-200' };
    default:
      return { icon: AlertTriangle, label: '未知', className: 'bg-gray-100 text-gray-700 border-gray-200' };
  }
};



/**
 * 获取字段样式类名
 */
const getFieldStyleClasses = (style: FieldStyle): string => {
  const classes = [];
  
  // 字体大小
  switch (style.fontSize) {
    case 'xs': classes.push('text-xs'); break;
    case 'sm': classes.push('text-sm'); break;
    case 'base': classes.push('text-base'); break;
    case 'lg': classes.push('text-lg'); break;
    case 'xl': classes.push('text-xl'); break;
  }
  
  // 字体粗细
  switch (style.fontWeight) {
    case 'normal': classes.push('font-normal'); break;
    case 'medium': classes.push('font-medium'); break;
    case 'semibold': classes.push('font-semibold'); break;
    case 'bold': classes.push('font-bold'); break;
  }
  
  // 颜色
  switch (style.color) {
    case 'default': classes.push('text-foreground'); break;
    case 'muted': classes.push('text-muted-foreground'); break;
    case 'primary': classes.push('text-primary'); break;
    case 'secondary': classes.push('text-secondary'); break;
    case 'destructive': classes.push('text-destructive'); break;
    case 'warning': classes.push('text-warning'); break;
    case 'success': classes.push('text-success'); break;
  }
  
  // 对齐方式
  switch (style.textAlign) {
    case 'left': classes.push('text-left'); break;
    case 'center': classes.push('text-center'); break;
    case 'right': classes.push('text-right'); break;
  }
  
  return classes.join(' ');
};

/**
 * 获取卡片样式类名
 */
const getCardStyleClasses = (config: TaskCardConfig): string => {
  const classes = ['relative overflow-hidden cursor-pointer'];
  
  // 主题
  switch (config.style.theme) {
    case 'modern':
      classes.push('bg-gradient-to-br from-slate-50 to-slate-100 border-slate-200');
      break;
    case 'glass':
      classes.push('bg-white/80 backdrop-blur-sm border-white/20');
      break;
    case 'gradient':
      classes.push('bg-gradient-to-br from-blue-50 via-white to-purple-50 border-blue-200');
      break;
    case 'dark':
      classes.push('bg-slate-800 border-slate-700 text-white');
      break;
    default:
      classes.push('bg-white border-gray-200');
  }
  
  // 圆角
  switch (config.style.borderRadius) {
    case 'none': classes.push('rounded-none'); break;
    case 'sm': classes.push('rounded-sm'); break;
    case 'md': classes.push('rounded-md'); break;
    case 'lg': classes.push('rounded-lg'); break;
    case 'xl': classes.push('rounded-xl'); break;
  }
  
  // 阴影
  switch (config.style.shadow) {
    case 'none': classes.push('shadow-none'); break;
    case 'sm': classes.push('shadow-sm'); break;
    case 'md': classes.push('shadow-md'); break;
    case 'lg': classes.push('shadow-lg'); break;
    case 'xl': classes.push('shadow-xl'); break;
  }
  
  // 动画
  switch (config.style.animation) {
    case 'subtle':
      classes.push('transition-all duration-200 hover:shadow-md');
      break;
    case 'smooth':
      classes.push('transition-all duration-300 hover:scale-[1.02] hover:shadow-lg');
      break;
    case 'bouncy':
      classes.push('transition-all duration-300 hover:scale-105 hover:shadow-xl hover:rotate-1');
      break;
    default:
      classes.push('transition-all duration-150 ease-out');
  }
  
  return classes.join(' ');
};

/**
 * 获取间距类名
 */
const getSpacingClasses = (config: TaskCardConfig): string => {
  switch (config.style.spacing) {
    case 'tight': return 'p-1 space-y-1';
    case 'loose': return 'p-2 space-y-2';
    default: return 'p-2 space-y-1';
  }
};

/**
 * 可配置任务卡片组件
 */
export const ConfigurableTaskCard = memo<ConfigurableTaskCardProps>(({
  task,
  vehicles,
  config,
  size = 'small',
  className,
  vehicleDisplayMode = 'compact' as VehicleDisplayMode,
  inTaskVehicleCardStyles = {
    cardWidth: 'w-14',
    cardHeight: 'h-8',
    fontSize: 'text-[10px]',
    fontColor: 'text-foreground',
    vehicleNumberFontWeight: 'font-medium',
    cardBgColor: 'bg-background',
    statusDotSize: 'w-1 h-1',
    borderRadius: 'rounded-sm',
    boxShadow: 'shadow-sm'
  },
  density = 'normal',
  onTaskContextMenu,
  onTaskDoubleClick,
  onDropVehicleOnLine,
  onCancelDispatch,
  onOpenStyleEditor,
  onOpenDeliveryOrderDetails,
  onOpenVehicleContextMenu
}) => {
  const [showProductionPanel, setShowProductionPanel] = React.useState(false);
  const [showMessageModal, setShowMessageModal] = React.useState(false);
  const { settings } = useTaskListSettings();

  const statusInfo = useMemo(() => getStatusInfo(task.dispatchStatus), [task.dispatchStatus]);
  const StatusIcon = statusInfo.icon;

  // 动态计算卡片尺寸 - 简化为只支持 small 规格
  const getCardDimensions = useMemo(() => {
    // 计算内容高度需求
    const calculateContentHeight = () => {
      let height = 0;

      // 顶部区域高度
      if (config.areas?.top?.visible !== false) {
        const baseHeight = config.style?.spacing === 'tight' ? 32 :
                          config.style?.spacing === 'loose' ? 48 : 40;
        height += baseHeight;
      }

      // 车辆区域高度（固定）
      height += 80; // 两行三列车辆区域，小尺寸

      // 内容区域高度
      if (config.areas?.content?.visible !== false) {
        const contentFields = Object.values(config.areas?.content?.fields || {}).filter(field => field?.visible !== false);
        const fieldsPerColumn = config.areas?.content?.layout === 'double' ? Math.ceil(contentFields.length / 2) : contentFields.length;
        const baseRowHeight = config.style?.spacing === 'tight' ? 16 :
                              config.style?.spacing === 'loose' ? 26 : 20;
        height += fieldsPerColumn * baseRowHeight;
      }

      // 底部区域高度
      if (config.areas?.bottom?.visible !== false) {
        const bottomFields = Object.values(config.areas?.bottom?.fields || {}).filter(field => field?.visible !== false);
        const fieldsPerColumn = config.areas?.bottom?.layout === 'double' ? Math.ceil(bottomFields.length / 2) : bottomFields.length;
        const baseRowHeight = config.style?.spacing === 'tight' ? 14 :
                              config.style?.spacing === 'loose' ? 22 : 18;
        height += fieldsPerColumn * baseRowHeight;
      }

      // 内边距
      const basePadding = config.style?.spacing === 'tight' ? 12 :
                         config.style?.spacing === 'loose' ? 24 : 16;
      height += basePadding;

      return Math.max(height, 200); // 最小高度 200px
    };

    // 计算宽度需求
    const calculateWidth = () => {
      const isDoubleLayout = config.areas?.content?.layout === 'double' ||
                           config.areas?.bottom?.layout === 'double';

      // 小卡片的基础宽度
      let baseWidth = isDoubleLayout ? 200 : 160;

      // 根据间距调整
      if (config.style?.spacing === 'tight') {
        baseWidth -= 16;
      } else if (config.style?.spacing === 'loose') {
        baseWidth += 32;
      }

      return Math.max(baseWidth, 140); // 最小宽度 140px
    };

    const width = calculateWidth();
    const height = calculateContentHeight();

    return {
      width,
      height,
      className: `w-[${width}px] min-h-[${height}px]`
    };
  }, [config]);
  
  // 计算进度百分比
  const progressPercentage = useMemo(() => {
    if (task.requiredVolume === 0) return 0;
    return Math.round((task.completedVolume / task.requiredVolume) * 100);
  }, [task.completedVolume, task.requiredVolume]);

  // 计算发车提醒信息
  const dispatchReminderInfo = useMemo(() => {
    if (task.dispatchStatus !== 'InProgress') return null;
    if (!task.dispatchFrequencyMinutes || task.dispatchFrequencyMinutes <= 0) return null;
    if (!task.nextScheduledDispatchTime) return null;

    const now = new Date();
    const nextDispatchTime = new Date(task.nextScheduledDispatchTime);
    const totalSecondsDiff = differenceInSeconds(nextDispatchTime, now);

    if (totalSecondsDiff <= 0) {
      return { text: '立即发车', type: 'urgent' as const, icon: AlertTriangle };
    }

    const minutesDiff = Math.floor(totalSecondsDiff / 60);
    const secondsDiff = totalSecondsDiff % 60;

    if (minutesDiff <= 5) {
      return {
        text: `${minutesDiff}:${secondsDiff.toString().padStart(2, '0')}`,
        type: 'urgent' as const,
        icon: AlertTriangle
      };
    } else if (minutesDiff <= 15) {
      return { text: `距发:${minutesDiff}分钟`, type: 'warning' as const, icon: Clock };
    } else {
      return { text: `距发:${minutesDiff}分钟`, type: 'normal' as const, icon: Clock };
    }
  }, [task.dispatchStatus, task.dispatchFrequencyMinutes, task.nextScheduledDispatchTime]);

  // 拖拽目标设置
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: ItemTypes.VEHICLE_CARD_DISPATCH,
    drop: (item: any, monitor) => {
      if (monitor.didDrop()) return { dropped: true };

      // 处理车辆拖拽到任务卡片
      if (item.vehicle && onDropVehicleOnLine) {
        // 默认分配到第一条生产线
        onDropVehicleOnLine(item.vehicle, task.id, 'L1');
        return { dropped: true };
      }
    },
    canDrop: () => task.dispatchStatus === 'InProgress',
    collect: (monitor) => ({
      isOver: monitor.isOver({ shallow: true }),
      canDrop: monitor.canDrop(),
    }),
  });

  // 当有拖拽悬停时显示生产线面板 - 优化逻辑防止频繁弹出收起
  React.useEffect(() => {
    if (isOver && canDrop) {
      setShowProductionPanel(true);
    } else if (!isOver && !canDrop) {
      // 只有在完全离开拖拽区域时才隐藏面板
      const timer = setTimeout(() => {
        setShowProductionPanel(false);
      }, 300); // 增加延迟时间
      return () => clearTimeout(timer);
    }
  }, [isOver, canDrop]);

  // 优化事件处理器
  const handleTaskContextMenu = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    onTaskContextMenu?.(e, task);
  }, [onTaskContextMenu, task]);

  const handleTaskDoubleClick = useCallback(() => {
    onTaskDoubleClick?.(task);
  }, [onTaskDoubleClick, task]);

  const handleCancelDispatch = useCallback((vehicleId: string) => {
    onCancelDispatch?.(vehicleId);
  }, [onCancelDispatch]);

  const handleOpenDeliveryOrderDetails = useCallback((vehicleId: string) => {
    onOpenDeliveryOrderDetails?.(vehicleId, task.id);
  }, [onOpenDeliveryOrderDetails, task.id]);

  const handleOpenVehicleContextMenu = useCallback((e: React.MouseEvent, vehicle: Vehicle) => {
    onOpenVehicleContextMenu?.(e, vehicle, task);
  }, [onOpenVehicleContextMenu, task]);

  const handleMessageIconClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation(); // 防止触发卡片点击事件
    setShowMessageModal(true);
  }, []);

  const handleMarkAsRead = useCallback((messageId: string) => {
    // TODO: 实现标记消息为已读的逻辑
    console.log('Mark message as read:', messageId);
  }, []);

  const handleMarkAllAsRead = useCallback((taskId: string) => {
    // TODO: 实现标记所有消息为已读的逻辑
    console.log('Mark all messages as read for task:', taskId);
  }, []);

  // 缓存拖拽样式
  const dragStyles = React.useMemo(() => {
    if (isOver && canDrop) {
      return 'ring-2 ring-primary shadow-lg bg-primary/5';
    }
    return '';
  }, [isOver, canDrop]);

  return (
    <div className={cn(
          'relative z-0 ',
        )} ref={drop as any}>
      <Card
        className={cn(
          getCardStyleClasses(config),
          'flex flex-col', // 弹性布局
          getCardDimensions.className, // 根据配置动态设置宽度和高度
          dragStyles,
          className
        )}
        onContextMenu={handleTaskContextMenu}
        onDoubleClick={handleTaskDoubleClick}
      >
        <CardContent className={cn('flex-1 flex flex-col', getSpacingClasses(config))}>
          {/* 顶部区域 - 优化布局 */}
          {config.areas.top.visible && (
            <div className="border-b border-border/50 pb-2">
              <div className="flex items-start justify-between">
                {/* 左侧：消息图标 + 项目信息 */}
                <div className="min-w-0 flex-1 flex items-start gap-2">
                  {/* 消息图标 */}
                  {config.areas?.top?.fields?.messageIcon?.visible && (
                    <div
                      className="relative flex-shrink-0 mt-0.5 cursor-pointer hover:bg-gray-100 rounded p-1 -m-1 transition-colors"
                      onClick={handleMessageIconClick}
                      title={task.hasNewMessages ? `${task.unreadMessageCount} 条未读消息` : '查看消息'}
                    >
                      <MessageCircle
                        className={cn(
                          'w-4 h-4',
                          config.areas?.top?.fields?.messageIcon ? getFieldStyleClasses(config.areas.top.fields.messageIcon) : '',
                          task.hasNewMessages ? 'text-blue-600' : 'text-muted-foreground'
                        )}
                      />
                      {/* 消息角标 */}
                      {task.hasNewMessages && task.unreadMessageCount && task.unreadMessageCount > 0 && (
                        <div className="absolute -top-1 -right-1 bg-red-500 text-white text-[10px] font-medium rounded-full min-w-[16px] h-4 flex items-center justify-center px-1">
                          {task.unreadMessageCount > 9 ? '9+' : task.unreadMessageCount}
                        </div>
                      )}
                    </div>
                  )}

                  {/* 项目信息 */}
                  <div className="min-w-0 flex-1">
                    {config.areas.top.fields.projectName.visible && (
                      <div className={cn('truncate text-sm font-semibold', getFieldStyleClasses(config.areas.top.fields.projectName))}>
                        {task.projectName || task.taskNumber}
                      </div>
                    )}
                    {config.areas.top.fields.constructionSite.visible && (
                      <div className={cn('truncate text-xs text-muted-foreground mt-0.5', getFieldStyleClasses(config.areas.top.fields.constructionSite))}>
                        {task.constructionSite}
                      </div>
                    )}
                  </div>
                </div>

                {/* 右侧：发车提醒 + 强度 分两行显示 */}
                <div className="flex flex-col gap-1 flex-shrink-0 items-end">
                  {/* 第一行：发车提醒 */}
                  {config.areas.top.fields.dispatchReminder.visible && dispatchReminderInfo && (
                    <div
                      className={cn(
                        'text-xs cursor-help flex items-center gap-1',
                        getFieldStyleClasses(config.areas.top.fields.dispatchReminder),
                        dispatchReminderInfo.type === 'urgent'
                          ? 'text-red-600'
                          : dispatchReminderInfo.type === 'warning'
                          ? 'text-yellow-600'
                          : 'text-muted-foreground'
                      )}
                      title={`下次发车时间: ${task.nextScheduledDispatchTime ? new Date(task.nextScheduledDispatchTime).toLocaleString() : '未计划'}`}
                    >
                      {React.createElement(dispatchReminderInfo.icon, { className: "w-3 h-3" })}
                      <span>{dispatchReminderInfo.text}</span>
                    </div>
                  )}

                  {/* 第二行：强度 */}
                  {config.areas.top.fields.strength.visible && (
                    <div className={cn('text-xs text-orange-600 flex items-center gap-1', getFieldStyleClasses(config.areas.top.fields.strength))}>
                      <Target className="w-3 h-3" />
                      <span>{task.strength}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* 调度车辆区域 - 两行三列布局 */}
          <div className="border-b border-border/50 pb-2">
            <div className="flex items-center gap-2 mb-2">
              <Truck className="w-4 h-4 text-blue-600" />
              {config.areas.vehicle.fields.vehicleCount.visible && (
                <span className={cn('text-sm font-medium', getFieldStyleClasses(config.areas.vehicle.fields.vehicleCount))}>
                  调度车辆 ({vehicles.length})
                </span>
              )}
            </div>
            <div className="h-[100px] bg-slate-50 border border-dashed border-slate-300 rounded-lg p-2 overflow-y-auto">
              {vehicles.length > 0 ? (
                <div className="grid grid-cols-3 gap-2 auto-rows-max">
                  {vehicles.map((vehicle) => (
                    <InTaskVehicleCard
                      key={vehicle.id}
                      vehicle={vehicle}
                      task={task}
                      vehicleDisplayMode={vehicleDisplayMode}
                      inTaskVehicleCardStyles={inTaskVehicleCardStyles}
                      productionLineCount={task.productionLineCount || 3}
                      density={density}
                      onCancelDispatch={handleCancelDispatch}
                      onOpenStyleEditor={onOpenStyleEditor}
                      onOpenDeliveryOrderDetails={handleOpenDeliveryOrderDetails}
                      onOpenContextMenu={handleOpenVehicleContextMenu}
                    />
                  ))}
                </div>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <span className="text-sm text-slate-500">拖拽车辆到此区域</span>
                </div>
              )}
            </div>
          </div>

          {/* 内容区域 - 优化双列布局 */}
          {config.areas.content.visible && (
            <div className="flex-1 border-b border-border/50 py-2">
              {config.areas.content.layout === 'double' ? (
                /* 双列布局 - 左右分栏 */
                <div className="grid grid-cols-2 gap-3">
                  {/* 左列 */}
                  <div className="space-y-1 pr-2 border-r border-border/30">
                    {config.areas.content.fields.requiredVolume.visible && (
                      <div className="flex flex-col text-xs py-0.5">
                        <span className="text-muted-foreground text-[10px]">需求方量</span>
                        <span className={cn('font-medium', getFieldStyleClasses(config.areas.content.fields.requiredVolume))}>
                          {task.requiredVolume}m³
                        </span>
                      </div>
                    )}
                    {config.areas.content.fields.completedVolume.visible && (
                      <div className="flex flex-col text-xs py-0.5">
                        <span className="text-muted-foreground text-[10px]">完成方量</span>
                        <span className={cn('font-medium', getFieldStyleClasses(config.areas.content.fields.completedVolume))}>
                          {task.completedVolume}m³
                        </span>
                      </div>
                    )}
                    {config.areas.content.fields.scheduledTime.visible && (
                      <div className="flex flex-col text-xs py-0.5">
                        <span className="text-muted-foreground text-[10px]">计划时间</span>
                        <span className={cn('font-medium', getFieldStyleClasses(config.areas.content.fields.scheduledTime))}>
                          {task.scheduledTime || '--'}
                        </span>
                      </div>
                    )}
                    {config.areas.content.fields.contactPhone.visible && (
                      <div className="flex flex-col text-xs py-0.5">
                        <span className="text-muted-foreground text-[10px]">联系电话</span>
                        <span className={cn('font-medium', getFieldStyleClasses(config.areas.content.fields.contactPhone))}>
                          {task.contactPhone || '--'}
                        </span>
                      </div>
                    )}
                  </div>
                
                  {/* 右列 */}
                  <div className="space-y-1 pl-2">
                    {config.areas.content.fields.completedProgress.visible && (
                      <div className="flex flex-col text-xs py-0.5">
                        <span className="text-muted-foreground text-[10px]">完成进度</span>
                        <span className={cn('font-medium', getFieldStyleClasses(config.areas.content.fields.completedProgress))}>
                          {progressPercentage}%
                        </span>
                      </div>
                    )}
                    {config.areas.content.fields.estimatedDuration.visible && (
                      <div className="flex flex-col text-xs py-0.5">
                        <span className="text-muted-foreground text-[10px]">预计时长</span>
                        <span className={cn('font-medium', getFieldStyleClasses(config.areas.content.fields.estimatedDuration))}>
                          --
                        </span>
                      </div>
                    )}
                    {config.areas.content.fields.constructionLocation.visible && (
                      <div className="flex flex-col text-xs py-0.5">
                        <span className="text-muted-foreground text-[10px]">施工地点</span>
                        <span className={cn('font-medium truncate', getFieldStyleClasses(config.areas.content.fields.constructionLocation))}>
                          {task.constructionSite}
                        </span>
                      </div>
                    )}
                    {config.areas.content.fields.taskStatus.visible && (
                      <div className="flex flex-col text-xs py-0.5">
                        <span className="text-muted-foreground text-[10px]">状态</span>
                        <Badge className={cn('text-xs px-1 py-0 mt-0.5', getFieldStyleClasses(config.areas.content.fields.taskStatus), statusInfo.className)}>
                          <StatusIcon className="w-2 h-2 mr-0.5" />
                          {statusInfo.label}
                        </Badge>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                /* 单列布局 */
                <div className="space-y-1">
                  {config.areas.content.fields.requiredVolume.visible && (
                    <div className="flex items-center justify-between text-xs py-0.5">
                      <span className="text-muted-foreground">需求方量</span>
                      <span className={cn('font-medium', getFieldStyleClasses(config.areas.content.fields.requiredVolume))}>
                        {task.requiredVolume}m³
                      </span>
                    </div>
                  )}
                  {config.areas.content.fields.completedVolume.visible && (
                    <div className="flex items-center justify-between text-xs py-0.5">
                      <span className="text-muted-foreground">完成方量</span>
                      <span className={cn('font-medium', getFieldStyleClasses(config.areas.content.fields.completedVolume))}>
                        {task.completedVolume}m³
                      </span>
                    </div>
                  )}
                  {config.areas.content.fields.scheduledTime.visible && (
                    <div className="flex items-center justify-between text-xs py-0.5">
                      <span className="text-muted-foreground">计划时间</span>
                      <span className={cn('font-medium', getFieldStyleClasses(config.areas.content.fields.scheduledTime))}>
                        {task.scheduledTime || '--'}
                      </span>
                    </div>
                  )}
                  {config.areas.content.fields.contactPhone.visible && (
                    <div className="flex items-center justify-between text-xs py-0.5">
                      <span className="text-muted-foreground">联系电话</span>
                      <span className={cn('font-medium', getFieldStyleClasses(config.areas.content.fields.contactPhone))}>
                        {task.contactPhone || '--'}
                      </span>
                    </div>
                  )}
                  {config.areas.content.fields.completedProgress.visible && (
                    <div className="flex items-center justify-between text-xs py-0.5">
                      <span className="text-muted-foreground">完成进度</span>
                      <span className={cn('font-medium', getFieldStyleClasses(config.areas.content.fields.completedProgress))}>
                        {progressPercentage}%
                      </span>
                    </div>
                  )}
                  {config.areas.content.fields.estimatedDuration.visible && (
                    <div className="flex items-center justify-between text-xs py-0.5">
                      <span className="text-muted-foreground">预计时长</span>
                      <span className={cn('font-medium', getFieldStyleClasses(config.areas.content.fields.estimatedDuration))}>
                        --
                      </span>
                    </div>
                  )}
                  {config.areas.content.fields.constructionLocation.visible && (
                    <div className="flex items-center justify-between text-xs py-0.5">
                      <span className="text-muted-foreground">施工地点</span>
                      <span className={cn('font-medium truncate max-w-[150px]', getFieldStyleClasses(config.areas.content.fields.constructionLocation))}>
                        {task.constructionSite}
                      </span>
                    </div>
                  )}
                  {config.areas.content.fields.taskStatus.visible && (
                    <div className="flex items-center justify-between text-xs py-0.5">
                      <span className="text-muted-foreground">状态</span>
                      <Badge className={cn('text-xs px-1 py-0', getFieldStyleClasses(config.areas.content.fields.taskStatus), statusInfo.className)}>
                        <StatusIcon className="w-2 h-2 mr-0.5" />
                        {statusInfo.label}
                      </Badge>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* 底部区域 - 极简紧凑布局 */}
          {config.areas.bottom.visible && (
            <div className="pt-1 border-t border-border/30 bg-slate-50/30 -mx-2 -mb-2 px-2 pb-1">
              <div className={cn(
                'grid gap-1 text-xs',
                config.areas.bottom.layout === 'double' ? 'grid-cols-2' : 'grid-cols-1'
              )}>
                {/* 左列或单列 */}
                <div className="space-y-0.5">
                  {config.areas.bottom.fields.customerName.visible && (
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">客户</span>
                      <span className={cn('font-medium truncate max-w-[100px]', getFieldStyleClasses(config.areas.bottom.fields.customerName))}>
                        {task.customerName}
                      </span>
                    </div>
                  )}
                  {config.areas.bottom.fields.createdAt.visible && (
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">创建</span>
                      <span className={cn('font-medium', getFieldStyleClasses(config.areas.bottom.fields.createdAt))}>
                        {task.createdAt || '--'}
                      </span>
                    </div>
                  )}
                </div>

                {/* 右列（仅在双列布局时显示） */}
                {config.areas.bottom.layout === 'double' && (
                  <div className="space-y-0.5">
                    {config.areas.bottom.fields.taskNumber.visible && (
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">编号</span>
                        <span className={cn('font-medium truncate max-w-[100px]', getFieldStyleClasses(config.areas.bottom.fields.taskNumber))}>
                          {task.taskNumber}
                        </span>
                      </div>
                    )}
                    {config.areas.bottom.fields.updatedAt.visible && (
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">更新</span>
                        <span className={cn('font-medium', getFieldStyleClasses(config.areas.bottom.fields.updatedAt))}>
                          {task.createdAt || '--'}
                        </span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 生产线面板 - 半透明背景遮罩 */}
      {showProductionPanel && (
        <div
          className="absolute inset-0 bg-black/20 backdrop-blur-sm rounded-lg"
          style={{ zIndex: 5 }}
        />
      )}

      {/* 生产线面板 */}
      <div
        className={cn(
          'absolute top-0 right-0 h-full w-56 bg-white/95 backdrop-blur-md',
          'border-l-2 border-blue-200 shadow-2xl rounded-r-lg',
          'transform transition-all duration-500 ease-in-out',
          'translate-x-full opacity-0',
          showProductionPanel && 'translate-x-0 opacity-100'
        )}
        style={{ zIndex: 10 }}
      >
        <div className="p-5 h-full flex flex-col">
          {/* 头部 */}
          <div className="flex items-center gap-3 mb-6 pb-3 border-b-2 border-blue-100">
            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-lg">
              <Factory className="w-5 h-5 text-white" />
            </div>
            <div>
              <span className="font-bold text-lg text-gray-800">选择生产线</span>
              <div className="text-xs text-gray-500">拖拽车辆到生产线</div>
            </div>
          </div>

          {/* 生产线列表 */}
          <div className="flex-1 space-y-4 overflow-y-auto">
            {Array.from({ length: task.productionLineCount || 3 }).map((_, index) => {
              const lineId = `L${index + 1}`;
              return (
                <ProductionLineDropZone
                  key={index}
                  lineId={lineId}
                  lineNumber={index + 1}
                  taskId={task.id}
                  onDropVehicle={onDropVehicleOnLine}
                />
              );
            })}
          </div>

          {/* 底部提示 */}
          <div className="mt-6 pt-4 border-t-2 border-blue-100">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <div className="w-2 h-2 rounded-full bg-blue-400 animate-pulse"></div>
              <span>拖拽车辆到生产线完成发车</span>
            </div>
            <div className="text-xs text-gray-400 mt-1">
              任务: {task.taskNumber}
            </div>
          </div>
        </div>
      </div>

      {/* 消息弹窗 */}
      <TaskMessageModal
        open={showMessageModal}
        onOpenChange={setShowMessageModal}
        task={task}
        onMarkAsRead={handleMarkAsRead}
        onMarkAllAsRead={handleMarkAllAsRead}
      />
    </div>
  );
}, (prevProps, nextProps) => {
  // 自定义比较函数，只在关键属性变化时重新渲染
  return (
    prevProps.task.id === nextProps.task.id &&
    prevProps.task.taskNumber === nextProps.task.taskNumber &&
    prevProps.task.projectName === nextProps.task.projectName &&
    prevProps.task.dispatchStatus === nextProps.task.dispatchStatus &&
    prevProps.task.completedVolume === nextProps.task.completedVolume &&
    prevProps.task.requiredVolume === nextProps.task.requiredVolume &&
    prevProps.task.isDueForDispatch === nextProps.task.isDueForDispatch &&
    prevProps.vehicles.length === nextProps.vehicles.length &&
    prevProps.vehicles.every((vehicle, index) =>
      vehicle.id === nextProps.vehicles[index]?.id &&
      vehicle.status === nextProps.vehicles[index]?.status
    ) &&
    JSON.stringify(prevProps.config) === JSON.stringify(nextProps.config) &&
    prevProps.vehicleDisplayMode === nextProps.vehicleDisplayMode &&
    prevProps.density === nextProps.density
  );
});

ConfigurableTaskCard.displayName = 'ConfigurableTaskCard';
