{"version": "1.0.0", "lastUpdated": "2025-01-21", "description": "站点默认UI配置，包含表格和卡片的所有样式设置", "taskListSettings": {"displayMode": "table", "density": "", "enableZebraStriping": false, "selectedPlantId": null, "columnOrder": ["messages", "dispatchReminder", "taskNumber", "projectName", "constructionSite", "strength", "pouring<PERSON><PERSON>od", "completedProgress", "requiredVolume", "completedVolume", "pumpTruck", "constructionUnit", "contactPhone", "supplyTime", "supplyDate", "publishDate", "dispatchedVehicles"], "columnVisibility": {"messages": true, "dispatchReminder": true, "taskNumber": true, "projectName": true, "constructionSite": true, "strength": true, "pouringMethod": true, "completedProgress": true, "requiredVolume": false, "completedVolume": false, "pumpTruck": true, "constructionUnit": true, "contactPhone": false, "supplyTime": true, "supplyDate": true, "publishDate": false, "dispatchedVehicles": true}, "columnWidths": {"messages": 40, "dispatchReminder": 60, "taskNumber": 120, "projectName": 150, "constructionSite": 120, "strength": 80, "pouringMethod": 100, "completedProgress": 120, "requiredVolume": 100, "completedVolume": 100, "pumpTruck": 80, "constructionUnit": 120, "contactPhone": 120, "supplyTime": 100, "supplyDate": 100, "publishDate": 100, "dispatchedVehicles": 300}, "columnTextStyles": {}, "columnBackgrounds": {}, "inTaskVehicleCardStyles": {"cardWidth": "w-14", "cardHeight": "h-8", "fontSize": "text-[10px]", "fontColor": "text-foreground", "vehicleNumberFontWeight": "font-medium", "cardBgColor": "bg-card/80", "cardGradient": null, "statusDotSize": "w-1 h-1", "borderRadius": "rounded-md", "boxShadow": "shadow-sm", "vehiclesPerRow": 4}, "groupConfig": {"groupBy": "none", "enabled": false, "collapsible": true, "defaultCollapsed": [], "sortOrder": "asc", "showGroupStats": true, "allowedGroupColumns": ["projectName", "strength", "pouring<PERSON><PERSON>od", "supplyDate", "pumpTruck", "constructionUnit", "constructionSite"], "disallowedGroupColumns": ["taskNumber", "vehicleCount", "completedVolume", "requiredVolume", "contactPhone", "supplyTime", "publishDate", "dispatchedVehicles"], "groupHeaderStyle": {"backgroundColor": "bg-muted/50", "textColor": "text-foreground", "fontSize": "text-sm", "fontWeight": "font-medium", "padding": "py-2"}}}, "taskCardConfig": {"style": {"theme": "default", "borderRadius": "md", "shadow": "sm", "animation": "subtle", "spacing": "normal"}, "areas": {"top": {"visible": true, "fields": {"messageIcon": {"visible": true, "fontSize": "sm", "fontWeight": "normal", "color": "primary", "textAlign": "left"}, "projectName": {"visible": true, "fontSize": "sm", "fontWeight": "semibold", "color": "default", "textAlign": "left"}, "constructionSite": {"visible": true, "fontSize": "xs", "fontWeight": "normal", "color": "muted", "textAlign": "left"}, "dispatchReminder": {"visible": true, "fontSize": "xs", "fontWeight": "medium", "color": "warning", "textAlign": "right"}, "strength": {"visible": true, "fontSize": "xs", "fontWeight": "medium", "color": "warning", "textAlign": "right"}, "progressRing": {"visible": true, "fontSize": "sm", "fontWeight": "medium", "color": "primary", "textAlign": "center"}}}, "vehicle": {"visible": true, "height": "normal", "fields": {"vehicleCount": {"visible": true, "fontSize": "sm", "fontWeight": "medium", "color": "default", "textAlign": "left"}, "vehicleCards": {"visible": true, "fontSize": "xs", "fontWeight": "normal", "color": "default", "textAlign": "left"}}}, "content": {"visible": true, "height": "auto", "layout": "double", "fields": {"requiredVolume": {"visible": true, "fontSize": "xs", "fontWeight": "medium", "color": "default", "textAlign": "left"}, "completedVolume": {"visible": true, "fontSize": "xs", "fontWeight": "medium", "color": "default", "textAlign": "left"}, "scheduledTime": {"visible": true, "fontSize": "xs", "fontWeight": "medium", "color": "default", "textAlign": "left"}, "contactPhone": {"visible": true, "fontSize": "xs", "fontWeight": "medium", "color": "default", "textAlign": "left"}, "completedProgress": {"visible": true, "fontSize": "xs", "fontWeight": "medium", "color": "default", "textAlign": "left"}, "estimatedDuration": {"visible": false, "fontSize": "xs", "fontWeight": "medium", "color": "default", "textAlign": "left"}, "constructionLocation": {"visible": false, "fontSize": "xs", "fontWeight": "medium", "color": "default", "textAlign": "left"}, "taskStatus": {"visible": false, "fontSize": "xs", "fontWeight": "medium", "color": "default", "textAlign": "left"}}}, "bottom": {"visible": true, "layout": "single", "fields": {"customerName": {"visible": true, "fontSize": "xs", "fontWeight": "normal", "color": "muted", "textAlign": "left"}, "createdAt": {"visible": true, "fontSize": "xs", "fontWeight": "normal", "color": "muted", "textAlign": "left"}, "taskNumber": {"visible": true, "fontSize": "xs", "fontWeight": "normal", "color": "muted", "textAlign": "left"}, "updatedAt": {"visible": false, "fontSize": "xs", "fontWeight": "normal", "color": "muted", "textAlign": "left"}}}}}, "cardViewConfig": {"size": "small", "layout": "standard", "theme": "default", "spacing": "normal", "borderRadius": "medium", "shadow": "medium", "animation": "smooth", "columns": "auto"}, "performanceConfig": {"virtualScroll": {"enabled": true, "overscan": 1, "increaseViewportBy": 100, "itemHeight": 300}, "animations": {"enabled": true, "duration": 250, "easing": "ease-out", "reduceMotion": false}, "rendering": {"useMemo": true, "useCallback": true, "reactMemo": true, "debounceMs": 100}, "scrolling": {"smoothScrolling": true, "scrollBehavior": "smooth", "throttleMs": 16}}}