// src/components/sections/task-list/cells/DispatchReminderCell.tsx
'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import type { Task } from '@/types';
import { cn } from '@/lib/utils';
import { differenceInMinutes, differenceInSeconds, formatDistanceToNowStrict } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { Clock, AlertTriangle } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface DispatchReminderCellProps {
  task: Task;
  textClassName?: string;
}

export const DispatchReminderCell: React.FC<DispatchReminderCellProps> = ({ task, textClassName }) => {
  const [countdown, setCountdown] = useState<string>('');
  const [countdownType, setCountdownType] = useState<'normal' | 'warning' | 'urgent' | 'muted'>('normal');
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const lastUpdateRef = useRef<number>(0);

  const updateCountdown = useCallback(() => {
    const now = new Date();
    lastUpdateRef.current = now.getTime();
    let newIntervalDelay = 30000; // Default update interval

    // Rule 1: No dispatch frequency set
    if (!task.dispatchFrequencyMinutes || task.dispatchFrequencyMinutes <= 0) {
      if (!task.lastDispatchTime) {
        setCountdown('-');
        setCountdownType('muted');
      } else {
        const lastDispatchDate = new Date(task.lastDispatchTime);
        const minutesSince = differenceInMinutes(now, lastDispatchDate);
        let displayTimeSince: string;
        if (minutesSince < 1) displayTimeSince = "<1分";
        else if (minutesSince < 60) displayTimeSince = `${minutesSince}分`;
        else displayTimeSince = `${Math.floor(minutesSince / 60)}时${minutesSince % 60}分`;
        setCountdown(`前：${displayTimeSince}`);
        setCountdownType('muted');
      }
      if (timerRef.current) clearInterval(timerRef.current); // No periodic update needed for this static info
      return;
    }

    // Rule 2: Dispatch frequency IS set
    const nextScheduledTime = task.nextScheduledDispatchTime ? new Date(task.nextScheduledDispatchTime) : null;

    if (!nextScheduledTime) {
      setCountdown('待计算');
      setCountdownType('normal');
      if (timerRef.current) clearInterval(timerRef.current);
      return;
    }

    const totalSecondsDiff = differenceInSeconds(nextScheduledTime, now);

    if (totalSecondsDiff < 0) { // Overdue
      const overdueSecondsAbs = Math.abs(totalSecondsDiff);
      const ovMinutes = Math.floor(overdueSecondsAbs / 60);
      const ovHours = Math.floor(ovMinutes / 60);
      let displayOverdue: string;

      if (ovHours > 0) {
        displayOverdue = `${ovHours}时${ovMinutes % 60}分`;
      } else if (ovMinutes > 0) {
        displayOverdue = `${ovMinutes}分${overdueSecondsAbs % 60}秒`;
      } else {
        displayOverdue = `${overdueSecondsAbs}秒`;
      }
      setCountdown(`超：${displayOverdue}`);
      setCountdownType('urgent');
      newIntervalDelay = 1000; // Update frequently when overdue
    } else { // Countdown (totalSecondsDiff >= 0)
      const remainingMinutes = Math.floor(totalSecondsDiff / 60);
      let displayCountdown: string;

      if (remainingMinutes >= 60) { // 60 minutes or more
        const hours = Math.floor(remainingMinutes / 60);
        const mins = remainingMinutes % 60;
        displayCountdown = `${hours}时${mins}分`;
        newIntervalDelay = 60000; // Update every minute
      } else if (remainingMinutes > 0) { // 1-59 minutes
        displayCountdown = `${remainingMinutes}分`;
        newIntervalDelay = 30000; // Update every 30 seconds
      } else { // 0 minutes remaining (but totalSecondsDiff > 0 means < 60 seconds)
        displayCountdown = `即将发车`; // If less than a minute, show "即将发车"
        newIntervalDelay = 1000; // Update frequently
      }
      setCountdown(`剩：${displayCountdown}`);
      setCountdownType('warning');
    }
    
    if (timerRef.current) clearInterval(timerRef.current);
    timerRef.current = setInterval(updateCountdown, newIntervalDelay);

  }, [task.dispatchFrequencyMinutes, task.lastDispatchTime, task.nextScheduledDispatchTime]);

  useEffect(() => {
    updateCountdown(); // Initial call
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [updateCountdown]);

  if (!task.dispatchFrequencyMinutes && !task.lastDispatchTime && task.dispatchStatus !== 'InProgress') {
     return <div className="text-muted-foreground text-center">-</div>;
  }
   if (!countdown && task.dispatchStatus !== 'InProgress') {
    return <div className="text-muted-foreground text-center">-</div>;
  }
  if (countdown === '待计算' && task.dispatchStatus !== 'InProgress') {
     return <div className="text-muted-foreground text-center">-</div>;
  }


  const getTooltipContent = () => {
    const nextTime = task.nextScheduledDispatchTime ? new Date(task.nextScheduledDispatchTime).toLocaleString() : '未计划';
    const lastTime = task.lastDispatchTime ? new Date(task.lastDispatchTime).toLocaleString() : '从未';
    const freq = task.dispatchFrequencyMinutes ? `${task.dispatchFrequencyMinutes}分钟` : '未设置';
    
    return (
      <div className="space-y-1 text-xs">
        <div><strong>当前显示:</strong> {countdown} ({countdownType})</div>
        <div><strong>下次计划发车:</strong> {nextTime}</div>
        <div><strong>上次发车:</strong> {lastTime}</div>
        <div><strong>发车频率:</strong> {freq}</div>
        <div><strong>任务状态:</strong> {task.dispatchStatus}</div>
      </div>
    );
  };

  const icon = countdownType === 'urgent' ? <AlertTriangle className="w-3 h-3" /> : <Clock className="w-3 h-3" />;
  const colorClass = 
    countdownType === 'urgent' ? "text-destructive font-bold" :
    countdownType === 'warning' ? "text-amber-600 dark:text-amber-400 font-medium" :
    countdownType === 'muted' ? "text-slate-600 dark:text-slate-400" : 
    "text-muted-foreground"; 

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div 
            className={cn(
              "flex items-center justify-center space-x-1 cursor-help",
              colorClass,
              textClassName
            )}
            onClick={updateCountdown} // Allow manual refresh by clicking
          >
            {icon}
            <span>{countdown || '-'}</span>
          </div>
        </TooltipTrigger>
        <TooltipContent side="right" className="max-w-xs">
          {getTooltipContent()}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

