// src/components/sections/task-list/cells/DispatchReminderCell.tsx
'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import type { Task } from '@/types';
import { cn } from '@/lib/utils';
import { differenceInMinutes, differenceInSeconds, formatDistanceToNowStrict } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { Clock, AlertTriangle } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface DispatchReminderCellProps {
  task: Task;
  textClassName?: string;
}

export const DispatchReminderCell: React.FC<DispatchReminderCellProps> = ({ task, textClassName }) => {
  const [countdown, setCountdown] = useState<string>('');
  const [countdownType, setCountdownType] = useState<'normal' | 'warning' | 'urgent' | 'muted'>('normal');
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const lastUpdateRef = useRef<number>(0);

  // 格式化时间显示，使用新的时间单位格式
  const formatTimeDisplay = useCallback((totalSeconds: number): string => {
    const absSeconds = Math.abs(totalSeconds);

    // 计算各个时间单位
    const months = Math.floor(absSeconds / (30 * 24 * 3600));
    const days = Math.floor((absSeconds % (30 * 24 * 3600)) / (24 * 3600));
    const hours = Math.floor((absSeconds % (24 * 3600)) / 3600);
    const minutes = Math.floor((absSeconds % 3600) / 60);
    const seconds = absSeconds % 60;

    // 根据时间长度选择合适的显示格式
    if (months > 0) {
      return days > 0 ? `${months}M${days}d` : `${months}M`;
    } else if (days > 0) {
      return hours > 0 ? `${days}d${hours}h` : `${days}d`;
    } else if (hours > 0) {
      return minutes > 0 ? `${hours}h${minutes}m` : `${hours}h`;
    } else if (minutes > 0) {
      return seconds > 0 ? `${minutes}m${seconds}s` : `${minutes}m`;
    } else {
      return `${seconds}s`;
    }
  }, []);

  const updateCountdown = useCallback(() => {
    const now = new Date();
    lastUpdateRef.current = now.getTime();
    let newIntervalDelay = 30000; // Default update interval

    // Rule 1: No dispatch frequency set
    if (!task.dispatchFrequencyMinutes || task.dispatchFrequencyMinutes <= 0) {
      if (!task.lastDispatchTime) {
        setCountdown('-');
        setCountdownType('muted');
      } else {
        const lastDispatchDate = new Date(task.lastDispatchTime);
        const secondsSince = differenceInSeconds(now, lastDispatchDate);
        const displayTimeSince = formatTimeDisplay(secondsSince);
        setCountdown(`前：${displayTimeSince}`);
        setCountdownType('muted');
      }
      if (timerRef.current) clearInterval(timerRef.current); // No periodic update needed for this static info
      return;
    }

    // Rule 2: Dispatch frequency IS set
    const nextScheduledTime = task.nextScheduledDispatchTime ? new Date(task.nextScheduledDispatchTime) : null;

    if (!nextScheduledTime) {
      setCountdown('-');
      setCountdownType('muted');
      if (timerRef.current) clearInterval(timerRef.current);
      return;
    }

    const totalSecondsDiff = differenceInSeconds(nextScheduledTime, now);

    if (totalSecondsDiff < 0) { // Overdue
      const displayOverdue = formatTimeDisplay(Math.abs(totalSecondsDiff));
      setCountdown(`超：${displayOverdue}`);
      setCountdownType('urgent');
      newIntervalDelay = 1000; // Update frequently when overdue
    } else { // Countdown (totalSecondsDiff >= 0)
      if (totalSecondsDiff < 60) {
        setCountdown(`剩：${totalSecondsDiff}s`);
        newIntervalDelay = 1000; // Update every second when less than a minute
      } else {
        const displayCountdown = formatTimeDisplay(totalSecondsDiff);
        setCountdown(`剩：${displayCountdown}`);
        newIntervalDelay = totalSecondsDiff < 3600 ? 1000 : 30000; // Update every second if less than an hour
      }
      setCountdownType('warning');
    }

    if (timerRef.current) clearInterval(timerRef.current);
    timerRef.current = setInterval(updateCountdown, newIntervalDelay);

  }, [task.dispatchFrequencyMinutes, task.lastDispatchTime, task.nextScheduledDispatchTime, formatTimeDisplay]);

  useEffect(() => {
    updateCountdown(); // Initial call
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [updateCountdown]);

  // 如果没有发车频率设置且没有上次发车时间，显示 "-"
  if (!task.dispatchFrequencyMinutes && !task.lastDispatchTime && task.dispatchStatus !== 'InProgress') {
     return <div className="text-muted-foreground text-center">-</div>;
  }

  // 如果没有倒计时内容，显示 "-"
  if (!countdown && task.dispatchStatus !== 'InProgress') {
    return <div className="text-muted-foreground text-center">-</div>;
  }


  const getTooltipContent = () => {
    const nextTime = task.nextScheduledDispatchTime ? new Date(task.nextScheduledDispatchTime).toLocaleString() : '未计划';
    const lastTime = task.lastDispatchTime ? new Date(task.lastDispatchTime).toLocaleString() : '从未';
    const freq = task.dispatchFrequencyMinutes ? `${task.dispatchFrequencyMinutes}分钟` : '未设置';
    
    return (
      <div className="space-y-1 text-xs">
        <div><strong>当前显示:</strong> {countdown} ({countdownType})</div>
        <div><strong>下次计划发车:</strong> {nextTime}</div>
        <div><strong>上次发车:</strong> {lastTime}</div>
        <div><strong>发车频率:</strong> {freq}</div>
        <div><strong>任务状态:</strong> {task.dispatchStatus}</div>
      </div>
    );
  };

  const icon = countdownType === 'urgent' ? <AlertTriangle className="w-3 h-3" /> : <Clock className="w-3 h-3" />;
  const colorClass =
    countdownType === 'urgent' ? "text-red-700 dark:text-red-600 font-bold" : // 深红色用于超时
    countdownType === 'warning' ? "text-amber-600 dark:text-amber-400 font-medium" : // 警告色用于剩余时间
    countdownType === 'muted' ? "text-slate-600 dark:text-slate-400" :
    "text-muted-foreground";

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div 
            className={cn(
              "flex items-center justify-center space-x-1 cursor-help",
              colorClass,
              textClassName
            )}
            onClick={updateCountdown} // Allow manual refresh by clicking
          >
            {icon}
            <span>{countdown || '-'}</span>
          </div>
        </TooltipTrigger>
        <TooltipContent side="right" className="max-w-xs">
          {getTooltipContent()}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

