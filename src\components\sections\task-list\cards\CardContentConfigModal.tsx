import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';
import {
  Layout,
  Eye,
  EyeOff,
  AlignVerticalJustifyStart,
  AlignVerticalJustifyCenter,
  AlignVerticalJustifyEnd,
  PanelLeftOpen,
  PanelLeftClose,
  Truck,
  Info,
  Target,
  PanelRight,
  Type,
  Palette,
  Settings,
  Move,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Bold,
  Italic,
} from 'lucide-react';

/**
 * 字段样式配置
 */
export interface FieldStyle {
  /** 字体大小 */
  fontSize: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl';
  /** 字体粗细 */
  fontWeight: 'normal' | 'medium' | 'semibold' | 'bold';
  /** 文字颜色 */
  color: 'default' | 'muted' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'custom';
  /** 自定义颜色值 (当 color 为 'custom' 时使用) */
  customColor?: string;
  /** 文字对齐 */
  textAlign: 'left' | 'center' | 'right';
  /** 是否显示 */
  visible: boolean;
  /** 字段位置 (在区域内的排序) */
  order: number;
  /** 字段宽度 */
  width: 'auto' | 'full' | 'half' | 'third' | 'quarter';
  /** 行高 */
  lineHeight: 'tight' | 'normal' | 'relaxed';
  /** 字母间距 */
  letterSpacing: 'tighter' | 'tight' | 'normal' | 'wide' | 'wider';
  /** 文字装饰 */
  textDecoration: 'none' | 'underline' | 'line-through';
  /** 文字变换 */
  textTransform: 'none' | 'uppercase' | 'lowercase' | 'capitalize';
  /** 边距 */
  margin: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  /** 内边距 */
  padding: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}

/**
 * 卡片内容配置接口
 * 专门负责卡片内容的显示和字段样式配置
 */
export interface CardContentConfig {
  /** 头部配置 */
  header: {
    show: boolean;
    height: 'compact' | 'normal' | 'expanded';
    fields: {
      status: FieldStyle;
      progress: FieldStyle;
      reminder: FieldStyle;
      taskId: FieldStyle;
      priority: FieldStyle;
    };
  };
  /** 主体配置 */
  body: {
    show: boolean;
    layout: 'info-only' | 'info-progress' | 'progress-only' | 'custom';
    progressPosition: 'center' | 'right' | 'left';
    fields: {
      taskInfo: FieldStyle;
      progressRing: FieldStyle;
      description: FieldStyle;
      location: FieldStyle;
      time: FieldStyle;
    };
  };
  /** 底部配置 */
  footer: {
    show: boolean;
    height: 'compact' | 'normal' | 'expanded';
    vehicleDisplayMode: 'grid' | 'list' | 'compact';
    fields: {
      vehicleCount: FieldStyle;
      vehicleList: FieldStyle;
      actions: FieldStyle;
    };
  };
  /** 右侧隐藏面板配置 */
  sidePanel: {
    enabled: boolean;
    width: 'narrow' | 'normal' | 'wide';
    opacity: number;
    showProductionLines: boolean;
    autoExpand: boolean;
    expandDuration: number;
    fields: {
      productionLines: FieldStyle;
      statistics: FieldStyle;
    };
  };
}

/**
 * 卡片内容配置模态框属性
 */
interface CardContentConfigModalProps {
  /** 是否打开 */
  open: boolean;
  /** 打开状态变化回调 */
  onOpenChange: (open: boolean) => void;
  /** 当前内容配置 */
  config: CardContentConfig;
  /** 配置变化回调 */
  onConfigChange: (config: CardContentConfig) => void;
}

/**
 * 卡片内容配置模态框组件
 * 专门负责卡片内容的显示和字段样式配置，实现所见即所得
 */
export const CardContentConfigModal: React.FC<CardContentConfigModalProps> = ({
  open,
  onOpenChange,
  config,
  onConfigChange,
}) => {
  // 本地配置状态，用于实时预览
  const [previewConfig, setPreviewConfig] = useState<CardContentConfig>(config);
  
  // 同步外部配置到预览配置
  React.useEffect(() => {
    setPreviewConfig(config);
  }, [config]);
  
  /**
   * 更新预览配置
   * @param newConfig 新的配置
   */
  const updatePreviewConfig = (newConfig: CardContentConfig) => {
    setPreviewConfig(newConfig);
  };
  
  /**
   * 应用配置
   */
  const applyConfig = () => {
    onConfigChange(previewConfig);
  };
  
  /**
   * 重置配置
   */
  const resetConfig = () => {
    setPreviewConfig(config);
  };

  /**
   * 配置选项组件
   */
  const ConfigSection: React.FC<{
    title: string;
    icon: React.ReactNode;
    children: React.ReactNode;
  }> = ({ title, icon, children }) => (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        {icon}
        <h3 className="font-medium text-sm">{title}</h3>
      </div>
      {children}
    </div>
  );

  /**
   * 字段样式配置组件
   */
  const FieldStyleConfig: React.FC<{
    label: string;
    fieldStyle: FieldStyle;
    onStyleChange: (style: FieldStyle) => void;
  }> = ({ label, fieldStyle, onStyleChange }) => (
    <div className="border rounded-lg p-3 space-y-3">
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium">{label}</Label>
        <Button
          variant={fieldStyle.visible ? 'default' : 'outline'}
          size="sm"
          onClick={() => onStyleChange({ ...fieldStyle, visible: !fieldStyle.visible })}
        >
          {fieldStyle.visible ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
          {fieldStyle.visible ? '显示' : '隐藏'}
        </Button>
      </div>
      
      {fieldStyle.visible && (
        <div className="grid grid-cols-2 gap-3">
          {/* 字体大小 */}
          <div className="space-y-2">
            <Label className="text-xs">字体大小</Label>
            <Select
              value={fieldStyle.fontSize}
              onValueChange={(value) => onStyleChange({ ...fieldStyle, fontSize: value as any })}
            >
              <SelectTrigger className="h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="xs">极小 (12px)</SelectItem>
                <SelectItem value="sm">小 (14px)</SelectItem>
                <SelectItem value="base">标准 (16px)</SelectItem>
                <SelectItem value="lg">大 (18px)</SelectItem>
                <SelectItem value="xl">特大 (20px)</SelectItem>
                <SelectItem value="2xl">超大 (24px)</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {/* 字体粗细 */}
          <div className="space-y-2">
            <Label className="text-xs">字体粗细</Label>
            <Select
              value={fieldStyle.fontWeight}
              onValueChange={(value) => onStyleChange({ ...fieldStyle, fontWeight: value as any })}
            >
              <SelectTrigger className="h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="normal">正常</SelectItem>
                <SelectItem value="medium">中等</SelectItem>
                <SelectItem value="semibold">半粗</SelectItem>
                <SelectItem value="bold">粗体</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {/* 文字颜色 */}
          <div className="space-y-2">
            <Label className="text-xs">文字颜色</Label>
            <Select
              value={fieldStyle.color}
              onValueChange={(value) => onStyleChange({ ...fieldStyle, color: value as any })}
            >
              <SelectTrigger className="h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="default">默认</SelectItem>
                <SelectItem value="muted">弱化</SelectItem>
                <SelectItem value="primary">主色</SelectItem>
                <SelectItem value="secondary">次要</SelectItem>
                <SelectItem value="success">成功</SelectItem>
                <SelectItem value="warning">警告</SelectItem>
                <SelectItem value="error">错误</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {/* 文字对齐 */}
          <div className="space-y-2">
            <Label className="text-xs">文字对齐</Label>
            <div className="flex gap-1">
              <Button
                variant={fieldStyle.textAlign === 'left' ? 'default' : 'outline'}
                size="sm"
                className="flex-1"
                onClick={() => onStyleChange({ ...fieldStyle, textAlign: 'left' })}
              >
                <AlignLeft className="w-3 h-3" />
              </Button>
              <Button
                variant={fieldStyle.textAlign === 'center' ? 'default' : 'outline'}
                size="sm"
                className="flex-1"
                onClick={() => onStyleChange({ ...fieldStyle, textAlign: 'center' })}
              >
                <AlignCenter className="w-3 h-3" />
              </Button>
              <Button
                variant={fieldStyle.textAlign === 'right' ? 'default' : 'outline'}
                size="sm"
                className="flex-1"
                onClick={() => onStyleChange({ ...fieldStyle, textAlign: 'right' })}
              >
                <AlignRight className="w-3 h-3" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  /**
   * 选项按钮组件
   */
  const OptionButton: React.FC<{
    value: string;
    currentValue: string;
    label: string;
    description?: string;
    icon?: React.ReactNode;
    onClick: () => void;
  }> = ({ value, currentValue, label, description, icon, onClick }) => (
    <Button
      variant={value === currentValue ? 'default' : 'outline'}
      className={cn(
        "h-auto p-3 flex flex-col items-center gap-2 text-center",
        value === currentValue && "ring-2 ring-primary/50"
      )}
      onClick={onClick}
    >
      {icon && <div className="text-lg">{icon}</div>}
      <div>
        <div className="font-medium text-xs">{label}</div>
        {description && (
          <div className="text-[10px] text-muted-foreground mt-1">
            {description}
          </div>
        )}
      </div>
    </Button>
  );

  // 默认字段样式
  const defaultFieldStyle: FieldStyle = {
    fontSize: 'sm',
    fontWeight: 'normal',
    color: 'default',
    textAlign: 'left',
    visible: true,
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Layout className="w-5 h-5" />
            卡片内容配置
          </DialogTitle>
          <DialogDescription>
            配置卡片内容的显示字段和样式，实现所见即所得的配置体验
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-6 pr-2">
          {/* 头部区域配置 */}
          <ConfigSection
            title="头部区域"
            icon={<AlignVerticalJustifyStart className="w-4 h-4 text-green-500" />}
          >
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Button
                  variant={previewConfig.header.show ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => updatePreviewConfig({
                    ...previewConfig,
                    header: { ...previewConfig.header, show: !previewConfig.header.show }
                  })}
                >
                  {previewConfig.header.show ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
                  {previewConfig.header.show ? '显示头部' : '隐藏头部'}
                </Button>
                
                {previewConfig.header.show && (
                  <div className="flex gap-2">
                    <OptionButton
                      value="compact"
                      currentValue={previewConfig.header.height}
                      label="紧凑"
                      onClick={() => updatePreviewConfig({
                        ...previewConfig,
                        header: { ...previewConfig.header, height: 'compact' }
                      })}
                    />
                    <OptionButton
                      value="normal"
                      currentValue={previewConfig.header.height}
                      label="标准"
                      onClick={() => updatePreviewConfig({
                        ...previewConfig,
                        header: { ...previewConfig.header, height: 'normal' }
                      })}
                    />
                    <OptionButton
                      value="expanded"
                      currentValue={previewConfig.header.height}
                      label="展开"
                      onClick={() => updatePreviewConfig({
                        ...previewConfig,
                        header: { ...previewConfig.header, height: 'expanded' }
                      })}
                    />
                  </div>
                )}
              </div>
              
              {previewConfig.header.show && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FieldStyleConfig
                    label="状态标识"
                    fieldStyle={previewConfig.header.fields.status}
                    onStyleChange={(style) => updatePreviewConfig({
                      ...previewConfig,
                      header: {
                        ...previewConfig.header,
                        fields: { ...previewConfig.header.fields, status: style }
                      }
                    })}
                  />
                  <FieldStyleConfig
                    label="进度信息"
                    fieldStyle={previewConfig.header.fields.progress}
                    onStyleChange={(style) => updatePreviewConfig({
                      ...previewConfig,
                      header: {
                        ...previewConfig.header,
                        fields: { ...previewConfig.header.fields, progress: style }
                      }
                    })}
                  />
                  <FieldStyleConfig
                    label="提醒标识"
                    fieldStyle={previewConfig.header.fields.reminder}
                    onStyleChange={(style) => updatePreviewConfig({
                      ...previewConfig,
                      header: {
                        ...previewConfig.header,
                        fields: { ...previewConfig.header.fields, reminder: style }
                      }
                    })}
                  />
                  <FieldStyleConfig
                    label="任务ID"
                    fieldStyle={previewConfig.header.fields.taskId}
                    onStyleChange={(style) => updatePreviewConfig({
                      ...previewConfig,
                      header: {
                        ...previewConfig.header,
                        fields: { ...previewConfig.header.fields, taskId: style }
                      }
                    })}
                  />
                  <FieldStyleConfig
                    label="优先级"
                    fieldStyle={previewConfig.header.fields.priority}
                    onStyleChange={(style) => updatePreviewConfig({
                      ...previewConfig,
                      header: {
                        ...previewConfig.header,
                        fields: { ...previewConfig.header.fields, priority: style }
                      }
                    })}
                  />
                </div>
              )}
            </div>
          </ConfigSection>

          <Separator />

          {/* 主体区域配置 */}
          <ConfigSection
            title="主体区域"
            icon={<AlignVerticalJustifyCenter className="w-4 h-4 text-blue-500" />}
          >
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Button
                  variant={previewConfig.body.show ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => updatePreviewConfig({
                    ...previewConfig,
                    body: { ...previewConfig.body, show: !previewConfig.body.show }
                  })}
                >
                  {previewConfig.body.show ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
                  {previewConfig.body.show ? '显示主体' : '隐藏主体'}
                </Button>
              </div>
              
              {previewConfig.body.show && (
                <>
                  <div className="grid grid-cols-2 gap-2">
                    <OptionButton
                      value="info-only"
                      currentValue={previewConfig.body.layout}
                      label="仅信息"
                      icon={<Info className="w-4 h-4" />}
                      onClick={() => updatePreviewConfig({
                        ...previewConfig,
                        body: { ...previewConfig.body, layout: 'info-only' }
                      })}
                    />
                    <OptionButton
                      value="progress-only"
                      currentValue={previewConfig.body.layout}
                      label="仅进度"
                      icon={<Target className="w-4 h-4" />}
                      onClick={() => updatePreviewConfig({
                        ...previewConfig,
                        body: { ...previewConfig.body, layout: 'progress-only' }
                      })}
                    />
                    <OptionButton
                      value="info-progress"
                      currentValue={previewConfig.body.layout}
                      label="信息+进度"
                      onClick={() => updatePreviewConfig({
                        ...previewConfig,
                        body: { ...previewConfig.body, layout: 'info-progress' }
                      })}
                    />
                    <OptionButton
                      value="custom"
                      currentValue={previewConfig.body.layout}
                      label="自定义"
                      onClick={() => updatePreviewConfig({
                        ...previewConfig,
                        body: { ...previewConfig.body, layout: 'custom' }
                      })}
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FieldStyleConfig
                      label="任务信息"
                      fieldStyle={previewConfig.body.fields.taskInfo}
                      onStyleChange={(style) => updatePreviewConfig({
                        ...previewConfig,
                        body: {
                          ...previewConfig.body,
                          fields: { ...previewConfig.body.fields, taskInfo: style }
                        }
                      })}
                    />
                    <FieldStyleConfig
                      label="进度环"
                      fieldStyle={previewConfig.body.fields.progressRing}
                      onStyleChange={(style) => updatePreviewConfig({
                        ...previewConfig,
                        body: {
                          ...previewConfig.body,
                          fields: { ...previewConfig.body.fields, progressRing: style }
                        }
                      })}
                    />
                    <FieldStyleConfig
                      label="描述信息"
                      fieldStyle={previewConfig.body.fields.description}
                      onStyleChange={(style) => updatePreviewConfig({
                        ...previewConfig,
                        body: {
                          ...previewConfig.body,
                          fields: { ...previewConfig.body.fields, description: style }
                        }
                      })}
                    />
                    <FieldStyleConfig
                      label="位置信息"
                      fieldStyle={previewConfig.body.fields.location}
                      onStyleChange={(style) => updatePreviewConfig({
                        ...previewConfig,
                        body: {
                          ...previewConfig.body,
                          fields: { ...previewConfig.body.fields, location: style }
                        }
                      })}
                    />
                    <FieldStyleConfig
                      label="时间信息"
                      fieldStyle={previewConfig.body.fields.time}
                      onStyleChange={(style) => updatePreviewConfig({
                        ...previewConfig,
                        body: {
                          ...previewConfig.body,
                          fields: { ...previewConfig.body.fields, time: style }
                        }
                      })}
                    />
                  </div>
                </>
              )}
            </div>
          </ConfigSection>

          <Separator />

          {/* 底部区域配置 */}
          <ConfigSection
            title="底部区域"
            icon={<AlignVerticalJustifyEnd className="w-4 h-4 text-purple-500" />}
          >
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Button
                  variant={previewConfig.footer.show ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => updatePreviewConfig({
                    ...previewConfig,
                    footer: { ...previewConfig.footer, show: !previewConfig.footer.show }
                  })}
                >
                  {previewConfig.footer.show ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
                  {previewConfig.footer.show ? '显示底部' : '隐藏底部'}
                </Button>
              </div>
              
              {previewConfig.footer.show && (
                <>
                  <div className="grid grid-cols-3 gap-2">
                    <OptionButton
                      value="compact"
                      currentValue={previewConfig.footer.height}
                      label="紧凑"
                      onClick={() => updatePreviewConfig({
                        ...previewConfig,
                        footer: { ...previewConfig.footer, height: 'compact' }
                      })}
                    />
                    <OptionButton
                      value="normal"
                      currentValue={previewConfig.footer.height}
                      label="标准"
                      onClick={() => updatePreviewConfig({
                        ...previewConfig,
                        footer: { ...previewConfig.footer, height: 'normal' }
                      })}
                    />
                    <OptionButton
                      value="expanded"
                      currentValue={previewConfig.footer.height}
                      label="展开"
                      onClick={() => updatePreviewConfig({
                        ...previewConfig,
                        footer: { ...previewConfig.footer, height: 'expanded' }
                      })}
                    />
                  </div>
                  
                  <div className="grid grid-cols-3 gap-2">
                    <OptionButton
                      value="grid"
                      currentValue={previewConfig.footer.vehicleDisplayMode}
                      label="网格"
                      onClick={() => updatePreviewConfig({
                        ...previewConfig,
                        footer: { ...previewConfig.footer, vehicleDisplayMode: 'grid' }
                      })}
                    />
                    <OptionButton
                      value="list"
                      currentValue={previewConfig.footer.vehicleDisplayMode}
                      label="列表"
                      onClick={() => updatePreviewConfig({
                        ...previewConfig,
                        footer: { ...previewConfig.footer, vehicleDisplayMode: 'list' }
                      })}
                    />
                    <OptionButton
                      value="compact"
                      currentValue={previewConfig.footer.vehicleDisplayMode}
                      label="紧凑"
                      onClick={() => updatePreviewConfig({
                        ...previewConfig,
                        footer: { ...previewConfig.footer, vehicleDisplayMode: 'compact' }
                      })}
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FieldStyleConfig
                      label="车辆数量"
                      fieldStyle={previewConfig.footer.fields.vehicleCount}
                      onStyleChange={(style) => updatePreviewConfig({
                        ...previewConfig,
                        footer: {
                          ...previewConfig.footer,
                          fields: { ...previewConfig.footer.fields, vehicleCount: style }
                        }
                      })}
                    />
                    <FieldStyleConfig
                      label="车辆列表"
                      fieldStyle={previewConfig.footer.fields.vehicleList}
                      onStyleChange={(style) => updatePreviewConfig({
                        ...previewConfig,
                        footer: {
                          ...previewConfig.footer,
                          fields: { ...previewConfig.footer.fields, vehicleList: style }
                        }
                      })}
                    />
                    <FieldStyleConfig
                      label="操作按钮"
                      fieldStyle={previewConfig.footer.fields.actions}
                      onStyleChange={(style) => updatePreviewConfig({
                        ...previewConfig,
                        footer: {
                          ...previewConfig.footer,
                          fields: { ...previewConfig.footer.fields, actions: style }
                        }
                      })}
                    />
                  </div>
                </>
              )}
            </div>
          </ConfigSection>

          <Separator />

          {/* 侧边面板配置 */}
          <ConfigSection
            title="侧边面板"
            icon={<PanelRight className="w-4 h-4 text-orange-500" />}
          >
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Button
                  variant={previewConfig.sidePanel.enabled ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => updatePreviewConfig({
                    ...previewConfig,
                    sidePanel: { ...previewConfig.sidePanel, enabled: !previewConfig.sidePanel.enabled }
                  })}
                >
                  {previewConfig.sidePanel.enabled ? <PanelLeftOpen className="w-4 h-4" /> : <PanelLeftClose className="w-4 h-4" />}
                  {previewConfig.sidePanel.enabled ? '启用面板' : '禁用面板'}
                </Button>
              </div>
              
              {previewConfig.sidePanel.enabled && (
                <>
                  <div className="grid grid-cols-3 gap-2">
                    <OptionButton
                      value="narrow"
                      currentValue={previewConfig.sidePanel.width}
                      label="窄"
                      onClick={() => updatePreviewConfig({
                        ...previewConfig,
                        sidePanel: { ...previewConfig.sidePanel, width: 'narrow' }
                      })}
                    />
                    <OptionButton
                      value="normal"
                      currentValue={previewConfig.sidePanel.width}
                      label="标准"
                      onClick={() => updatePreviewConfig({
                        ...previewConfig,
                        sidePanel: { ...previewConfig.sidePanel, width: 'normal' }
                      })}
                    />
                    <OptionButton
                      value="wide"
                      currentValue={previewConfig.sidePanel.width}
                      label="宽"
                      onClick={() => updatePreviewConfig({
                        ...previewConfig,
                        sidePanel: { ...previewConfig.sidePanel, width: 'wide' }
                      })}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label className="text-sm">透明度: {previewConfig.sidePanel.opacity}</Label>
                    <Slider
                      value={[previewConfig.sidePanel.opacity]}
                      onValueChange={([value]) => updatePreviewConfig({
                        ...previewConfig,
                        sidePanel: { ...previewConfig.sidePanel, opacity: value }
                      })}
                      min={0.1}
                      max={0.9}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label className="text-sm">展开动画时长: {previewConfig.sidePanel.expandDuration}ms</Label>
                    <Slider
                      value={[previewConfig.sidePanel.expandDuration]}
                      onValueChange={([value]) => updatePreviewConfig({
                        ...previewConfig,
                        sidePanel: { ...previewConfig.sidePanel, expandDuration: value }
                      })}
                      min={100}
                      max={1000}
                      step={50}
                      className="w-full"
                    />
                  </div>
                  
                  <div className="flex gap-2">
                    <Button
                      variant={previewConfig.sidePanel.showProductionLines ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => updatePreviewConfig({
                        ...previewConfig,
                        sidePanel: { ...previewConfig.sidePanel, showProductionLines: !previewConfig.sidePanel.showProductionLines }
                      })}
                    >
                      显示生产线
                    </Button>
                    <Button
                      variant={previewConfig.sidePanel.autoExpand ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => updatePreviewConfig({
                        ...previewConfig,
                        sidePanel: { ...previewConfig.sidePanel, autoExpand: !previewConfig.sidePanel.autoExpand }
                      })}
                    >
                      自动展开
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FieldStyleConfig
                      label="生产线信息"
                      fieldStyle={previewConfig.sidePanel.fields.productionLines}
                      onStyleChange={(style) => updatePreviewConfig({
                        ...previewConfig,
                        sidePanel: {
                          ...previewConfig.sidePanel,
                          fields: { ...previewConfig.sidePanel.fields, productionLines: style }
                        }
                      })}
                    />
                    <FieldStyleConfig
                      label="统计信息"
                      fieldStyle={previewConfig.sidePanel.fields.statistics}
                      onStyleChange={(style) => updatePreviewConfig({
                        ...previewConfig,
                        sidePanel: {
                          ...previewConfig.sidePanel,
                          fields: { ...previewConfig.sidePanel.fields, statistics: style }
                        }
                      })}
                    />
                  </div>
                </>
              )}
            </div>
          </ConfigSection>
        </div>

        {/* 底部操作按钮 */}
        <div className="flex justify-between pt-4 border-t">
          <Button variant="outline" onClick={resetConfig}>
            重置
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button onClick={applyConfig}>
              应用配置
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export type { CardContentConfig, FieldStyle, SidePanelConfig };
