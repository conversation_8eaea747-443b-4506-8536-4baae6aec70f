import type { Plant, Task, DeliveryOrder, DeliveryOrderStatus, Vehicle } from '@/types';
import {createCarNumber, generateRandomDate, generateRandomTime} from "@/lib/utils";

export const mockPlants: Plant[] = [
  { id: 'plant1', name: '搅拌站 A', stats: { completedTasks: 3, totalTasks: 5 }, productionLineCount: 2 },
  { id: 'plant2', name: '搅拌站 B', stats: { completedTasks: 7, totalTasks: 10 }, productionLineCount: 3 },
  { id: 'plant3', name: '搅拌站 C', stats: { completedTasks: 1, totalTasks: 2 }, productionLineCount: 1 },
  { id: 'plant4', name: '搅拌站 D', stats: { completedTasks: 0, totalTasks: 0 }, productionLineCount: 2 },
];

const dispatchStatuses: Task['dispatchStatus'][] = ['New', 'ReadyToProduce', 'RatioSet', 'InProgress', 'Paused', 'Completed', 'Cancelled'];
const projectNames = ['市中心高楼', '跨江大桥', '地铁隧道项目', '滨海新区开发', '科技产业园', '国际会展中心', '高速公路扩建', '体育馆新建', '老城区改造', '机场T3航站楼'];
const constructionUnits = ['建筑集团A', '路桥公司B', '市政建设总公司', '宏远地产', '新兴建设', '联合基建'];
const strengths = ['C25', 'C30', 'C35', 'C40', 'C45', 'C50'];
const pouringMethods = ['泵送', '塔吊', '车载泵', '自卸', '溜槽'];
const deliveryOrderStatuses: DeliveryOrderStatus[] = ['newlyDispatched', 'inProduction', 'weighed', 'shipped'];
const vehicleProductionStatuses: Vehicle['productionStatus'][] = ['queued', 'producing', 'produced', 'weighed', 'ticketed', 'shipped'];


export function generateMockTasks(): Task[] {
  const generatedTasks: Task[] = [];
  const stressTestTaskCount = 500;
  const stressTestTaskStartIndex = 0;

  for (let i = 0; i < stressTestTaskCount; i++) {
    const taskIndex = stressTestTaskStartIndex + i;
    const projectName = projectNames[Math.floor(Math.random() * projectNames.length)];
    const requiredVol = Math.floor(Math.random() * 200) + 50; 

    let dispatchFrequencyMinutes: number;
    let countdownMs: number;
    let isDueForDispatch = false;
    let taskStatus: Task['dispatchStatus'] = 'InProgress';
    let taskScenario = '';
    
    if (i < 20) {
      dispatchFrequencyMinutes = [5, 15, 30][i % 3];
      countdownMs = -(Math.floor(Math.random() * 10) + 1) * 60000; 
      isDueForDispatch = true;
      taskScenario = '已超时';
    } 
    else if (i < 40) {
      dispatchFrequencyMinutes = [5, 15, 30][i % 3];
      countdownMs = Math.floor(Math.random() * 60000); 
      isDueForDispatch = true;
      taskScenario = '即将发车(<1分钟)';
    } 
    else if (i < 60) {
      dispatchFrequencyMinutes = [15, 30, 45][i % 3];
      countdownMs = (1 + Math.floor(Math.random() * 4)) * 60000 + Math.floor(Math.random() * 59000); 
      isDueForDispatch = true;
      taskScenario = '紧急提醒(1-5分钟)';
    } 
    else if (i < 80) {
      dispatchFrequencyMinutes = [30, 45, 60][i % 3];
      countdownMs = (5 + Math.floor(Math.random() * 10)) * 60000 + Math.floor(Math.random() * 59000); 
      isDueForDispatch = false;
      taskScenario = '普通提醒(5-15分钟)';
    } 
    else if (i < 100) {
      dispatchFrequencyMinutes = [45, 60, 90][i % 3];
      countdownMs = (15 + Math.floor(Math.random() * 15)) * 60000 + Math.floor(Math.random() * 59000); 
      isDueForDispatch = false;
      taskScenario = '提前提醒(15-30分钟)';
    } 
    else if (i < 120) {
      dispatchFrequencyMinutes = [60, 90, 120][i % 3];
      countdownMs = (30 + Math.floor(Math.random() * 30)) * 60000 + Math.floor(Math.random() * 59000); 
      isDueForDispatch = false;
      taskScenario = '长时间任务(30-60分钟)';
    } 
    else if (i < 140) {
      dispatchFrequencyMinutes = [90, 120, 180][i % 3];
      countdownMs = (60 + Math.floor(Math.random() * 60)) * 60000 + Math.floor(Math.random() * 59000); 
      isDueForDispatch = false;
      taskScenario = '超长时间任务(60-120分钟)';
    } 
    else if (i < 160) {
      dispatchFrequencyMinutes = [30, 60, 90][i % 3];
      countdownMs = Math.floor(Math.random() * 60) * 60000 + Math.floor(Math.random() * 59000); 
      isDueForDispatch = false;
      taskStatus = 'Paused';
      taskScenario = '暂停任务';
    } 
    else if (i < 180) {
      dispatchFrequencyMinutes = [30, 60, 90][i % 3];
      countdownMs = Math.floor(Math.random() * 60) * 60000 + Math.floor(Math.random() * 59000); 
      isDueForDispatch = false;
      taskStatus = 'Completed';
      taskScenario = '已完成任务';
    } 
    else if (i < 200) {
      dispatchFrequencyMinutes = [30, 60, 90][i % 3];
      countdownMs = Math.floor(Math.random() * 60) * 60000 + Math.floor(Math.random() * 59000); 
      isDueForDispatch = false;
      taskStatus = 'Cancelled';
      taskScenario = '已取消任务';
    } 
    else if (i < 220) {
      dispatchFrequencyMinutes = [30, 60, 90][i % 3];
      countdownMs = Math.floor(Math.random() * 120) * 60000 + Math.floor(Math.random() * 59000); 
      isDueForDispatch = false;
      taskStatus = 'ReadyToProduce';
      taskScenario = '计划发车任务';
    } 
    else if (i < 240) {
      dispatchFrequencyMinutes = [2, 3, 4][i % 3]; 
      countdownMs = Math.floor(Math.random() * 2) * 60000 + Math.floor(Math.random() * 59000); 
      isDueForDispatch = countdownMs < 60000; 
      taskScenario = '多频次发车任务';
    }
    else if (i < 260) {
      dispatchFrequencyMinutes = [180, 240, 300][i % 3]; 
      countdownMs = Math.floor(Math.random() * 120) * 60000 + Math.floor(Math.random() * 59000); 
      isDueForDispatch = countdownMs < 300000; 
      taskScenario = '低频发车任务';
    }
    else {
      dispatchFrequencyMinutes = [5, 15, 30, 45, 60, 90, 120][Math.floor(Math.random() * 7)];
      countdownMs = Math.floor(Math.random() * 120) * 60000 + Math.floor(Math.random() * 59000); 
      isDueForDispatch = countdownMs < 5 * 60000; 
      taskScenario = '常规任务';
    }
    
    const nextScheduledTime = Date.now() + countdownMs;
    const lastDispatch = new Date(nextScheduledTime - dispatchFrequencyMinutes * 60 * 1000).toISOString();
    const nextScheduledTimeStr = new Date(nextScheduledTime).toISOString();
    
    const minutesToDispatch = Math.floor(countdownMs / 60000);

    generatedTasks.push({
      id: `task_stress_p1_${i + 1}`,
      plantId: 'plant1',
      taskNumber: `S1${String(taskIndex).padStart(4, '0')}`,
      projectName: `${taskScenario}-${projectName.substring(0,4)} #${i + 1}`,
      projectAbbreviation: `测${i + 1}`,
      constructionUnit: constructionUnits[Math.floor(Math.random() * constructionUnits.length)],
      constructionSite: `测试工地${Math.floor(Math.random() * 10) + 1}`,
      strength: strengths[Math.floor(Math.random() * strengths.length)],
      pouringMethod: pouringMethods[Math.floor(Math.random() * pouringMethods.length)],
      vehicleCount: Math.floor(Math.random() * 5) + 1,
      completedVolume: Math.floor(Math.random() * (requiredVol * 0.2)), 
      requiredVolume: requiredVol,
      pumpTruck: '无',
      otherRequirements: `${taskScenario}：测试发车提醒功能，频率${dispatchFrequencyMinutes}分钟，倒计时${minutesToDispatch}分钟`,
      contactPhone: `132${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
      supplyTime: generateRandomTime(),
      supplyDate: new Date().toISOString().split('T')[0], 
      publishDate: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString().split('T')[0], 
      dispatchStatus: taskStatus,
      isTicketed: true,
      dispatchFrequencyMinutes: dispatchFrequencyMinutes,
      lastDispatchTime: lastDispatch,
      nextScheduledDispatchTime: nextScheduledTimeStr, 
      isDueForDispatch: isDueForDispatch, 
      minutesToDispatch: minutesToDispatch, 
      productionLineCount: mockPlants.find(p => p.id === 'plant1')?.productionLineCount || 2,
    });
  }
  return generatedTasks;
}


export function generateMockDeliveryOrders(): DeliveryOrder[] {
  const generatedDeliveryOrders: DeliveryOrder[] = [];
  const tempTasks = generateMockTasks(); 
  let orderIdCounter = 1;

  mockPlants.forEach(plant => {
    for (let lineIndex = 0; lineIndex < plant.productionLineCount; lineIndex++) {
      const productionLineId = `L${lineIndex + 1}`;
      const ordersPerLine = Math.floor(Math.random() * 9) + 2; 

      const plantTasks = tempTasks.filter(t => t.plantId === plant.id);
      if (plantTasks.length === 0) continue; 

      for (let i = 0; i < ordersPerLine; i++) {
        const task = plantTasks[Math.floor(Math.random() * plantTasks.length)]; 
        
        generatedDeliveryOrders.push({
          id: `do_gen_${plant.id}_${productionLineId}_${orderIdCounter++}`,
          plantId: plant.id,
          productionLineId: productionLineId,
          vehicleNumber: createCarNumber(),
          driver: ['刘一', '陈二', '张三', '李四', '王五', '赵六', '孙七', '周八', '吴九', '郑十'][Math.floor(Math.random() * 10)],
          volume: Math.floor(Math.random() * 5) + 5, 
          strength: task.strength,
          projectName: task.projectName,
          taskNumber: task.taskNumber,
          status: deliveryOrderStatuses[Math.floor(Math.random() * deliveryOrderStatuses.length)],
        });
      }
    }
  });

  return generatedDeliveryOrders;
}

export function generateMockVehicles(): Vehicle[] {
  const generatedVehicles: Vehicle[] = [];
  const statusOptions: Vehicle['status'][] = ['pending', 'returned', 'outbound'];
  const operationalStatusOptions: NonNullable<Vehicle['operationalStatus']>[] = ['normal', 'paused', 'deactivated'];
  const tripTypeOptions: NonNullable<Vehicle['currentTripType']>[] = ['outboundLeg', 'returnLeg'];
  const plantIds = mockPlants.map(p => p.id);

  for (let i = 1; i <= 200; i++) { 
    const baseStatus = statusOptions[Math.floor(Math.random() * statusOptions.length)];
    let opStatus: Vehicle['operationalStatus'] = 'normal';
    let tripType: Vehicle['currentTripType'] = undefined;
    let prodStatus: Vehicle['productionStatus'] = undefined;
    let allowEdit = Math.random() < 0.3; 
    let deliveryId: string | undefined = undefined;
    let currentPlantId = plantIds[Math.floor(Math.random() * plantIds.length)];
    let assignedTaskId: string | undefined = undefined;
    let assignedProductionLineId: string | undefined = undefined;
    let lastTripWashedWithPumpWater = Math.random() < 0.1; // Approx 10% chance

     if (baseStatus === 'pending' || baseStatus === 'returned') {
      const rand = Math.random();
      if (rand < 0.1) opStatus = 'paused'; 
      else if (rand < 0.2) opStatus = 'deactivated';
      prodStatus = 'queued'; 
    } else if (baseStatus === 'outbound') {
      tripType = tripTypeOptions[Math.floor(Math.random() * tripTypeOptions.length)];
      prodStatus = vehicleProductionStatuses[Math.floor(Math.random() * vehicleProductionStatuses.length)];
      deliveryId = `do-v${i}`; 
      
      const tasksForPlant = generateMockTasks().filter(t => t.plantId === currentPlantId && t.dispatchStatus === 'InProgress');
      if (tasksForPlant.length > 0) {
        const randomTask = tasksForPlant[Math.floor(Math.random() * tasksForPlant.length)];
        assignedTaskId = randomTask.id;
        const plantDetails = mockPlants.find(p => p.id === currentPlantId);
        if (plantDetails && plantDetails.productionLineCount > 0) {
          assignedProductionLineId = `L${Math.floor(Math.random() * plantDetails.productionLineCount) + 1}`;
        }
      }
    }

     generatedVehicles.push({
      id: `v${i}`,
      vehicleNumber: i <= 100 ? String(i) : createCarNumber(), 
      status: baseStatus,
      type: i % 10 === 0 ? 'Pump' : 'Tanker',
      operationalStatus: opStatus,
      assignedTaskId: assignedTaskId,
      assignedProductionLineId: assignedProductionLineId,
      currentTripType: tripType,
      productionStatus: prodStatus,
      allowWeighRoomEdit: allowEdit,
      deliveryOrderId: deliveryId,
      plantId: currentPlantId,
      lastTripWashedWithPumpWater: lastTripWashedWithPumpWater,
    });
  }
  return generatedVehicles;
}


export const taskStatusOptions = [
  { value: "ReadyToProduce", label: "准备生产" },
  { value: "RatioSet", label: "已设定配比" },
  { value: "InProgress", label: "正在进行" },
  { value: "Paused", label: "暂停" },
  { value: "Completed", label: "已完成" },
  { value: "Cancelled", label: "已撤销" },
];

export const productTypeOptions = [
  { value: "All", label: "全部产品" },
  { value: "Concrete", label: "砼" },
  { value: "Mortar", label: "砂浆" },
];

export const defaultVolumeOptions = [
  { value: "Max", label: "最大方数" },
  { value: "NoOverload", label: "不超载" },
  { value: "Normal", label: "正常" },
];
