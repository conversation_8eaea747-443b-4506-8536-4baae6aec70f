'use client';

import React, { memo, useMemo, useCallback } from 'react';
import { ConfigurableTaskCard } from './ConfigurableTaskCard';
import { TaskCardConfig } from '@/types/taskCardConfig';
import type { Task, Vehicle, VehicleDisplayMode, InTaskVehicleCardStyle } from '@/types';

/**
 * 优化的任务卡片组件属性
 */
interface OptimizedTaskCardProps {
  task: Task;
  vehicles: Vehicle[];
  config: TaskCardConfig;
  size?: 'small' | 'medium' | 'large' | 'extra-large';
  className?: string;
  vehicleDisplayMode?: VehicleDisplayMode;
  inTaskVehicleCardStyles?: InTaskVehicleCardStyle;
  density?: 'compact' | 'normal' | 'loose';
  onTaskContextMenu?: (event: React.MouseEvent, task: Task) => void;
  onTaskDoubleClick?: (task: Task) => void;
  onDropVehicleOnLine?: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  onCancelDispatch?: (vehicleId: string) => void;
  onOpenStyleEditor?: () => void;
  onOpenDeliveryOrderDetails?: (vehicleId: string, taskId: string) => void;
  onOpenVehicleContextMenu?: (event: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
}

/**
 * 优化的任务卡片组件
 * 使用 React.memo 进行浅比较优化
 */
export const OptimizedTaskCard = memo<OptimizedTaskCardProps>(({
  task,
  vehicles,
  config,
  size = 'small',
  className,
  vehicleDisplayMode,
  inTaskVehicleCardStyles,
  density,
  onTaskContextMenu,
  onTaskDoubleClick,
  onDropVehicleOnLine,
  onCancelDispatch,
  onOpenStyleEditor,
  onOpenDeliveryOrderDetails,
  onOpenVehicleContextMenu
}) => {
  // 缓存车辆数据，避免不必要的重新渲染
  const memoizedVehicles = useMemo(() => vehicles, [vehicles]);
  
  // 缓存配置，避免不必要的重新渲染
  const memoizedConfig = useMemo(() => config, [config]);
  
  // 优化事件处理器，使用 useCallback 避免子组件重新渲染
  const handleTaskContextMenu = useCallback((event: React.MouseEvent, task: Task) => {
    onTaskContextMenu?.(event, task);
  }, [onTaskContextMenu]);
  
  const handleTaskDoubleClick = useCallback((task: Task) => {
    onTaskDoubleClick?.(task);
  }, [onTaskDoubleClick]);
  
  const handleDropVehicleOnLine = useCallback((vehicle: Vehicle, taskId: string, lineId: string) => {
    onDropVehicleOnLine?.(vehicle, taskId, lineId);
  }, [onDropVehicleOnLine]);
  
  const handleCancelDispatch = useCallback((vehicleId: string) => {
    onCancelDispatch?.(vehicleId);
  }, [onCancelDispatch]);
  
  const handleOpenStyleEditor = useCallback(() => {
    onOpenStyleEditor?.();
  }, [onOpenStyleEditor]);
  
  const handleOpenDeliveryOrderDetails = useCallback((vehicleId: string, taskId: string) => {
    onOpenDeliveryOrderDetails?.(vehicleId, taskId);
  }, [onOpenDeliveryOrderDetails]);
  
  const handleOpenVehicleContextMenu = useCallback((event: React.MouseEvent, vehicle: Vehicle, task: Task) => {
    onOpenVehicleContextMenu?.(event, vehicle, task);
  }, [onOpenVehicleContextMenu]);

  return (
    <ConfigurableTaskCard
      task={task}
      vehicles={memoizedVehicles}
      config={memoizedConfig}
      size={size}
      className={className}
      vehicleDisplayMode={vehicleDisplayMode}
      inTaskVehicleCardStyles={inTaskVehicleCardStyles}
      density={density}
      onTaskContextMenu={handleTaskContextMenu}
      onTaskDoubleClick={handleTaskDoubleClick}
      onDropVehicleOnLine={handleDropVehicleOnLine}
      onCancelDispatch={handleCancelDispatch}
      onOpenStyleEditor={handleOpenStyleEditor}
      onOpenDeliveryOrderDetails={handleOpenDeliveryOrderDetails}
      onOpenVehicleContextMenu={handleOpenVehicleContextMenu}
    />
  );
}, (prevProps, nextProps) => {
  // 自定义比较函数，只在关键属性变化时重新渲染
  return (
    prevProps.task.id === nextProps.task.id &&
    prevProps.task.taskNumber === nextProps.task.taskNumber &&
    prevProps.task.projectName === nextProps.task.projectName &&
    prevProps.task.dispatchStatus === nextProps.task.dispatchStatus &&
    prevProps.task.completedVolume === nextProps.task.completedVolume &&
    prevProps.task.requiredVolume === nextProps.task.requiredVolume &&
    prevProps.task.isDueForDispatch === nextProps.task.isDueForDispatch &&
    prevProps.task.nextScheduledDispatchTime === nextProps.task.nextScheduledDispatchTime &&
    prevProps.vehicles.length === nextProps.vehicles.length &&
    prevProps.vehicles.every((vehicle, index) => 
      vehicle.id === nextProps.vehicles[index]?.id &&
      vehicle.status === nextProps.vehicles[index]?.status
    ) &&
    prevProps.config === nextProps.config &&
    prevProps.vehicleDisplayMode === nextProps.vehicleDisplayMode &&
    prevProps.density === nextProps.density
  );
});

OptimizedTaskCard.displayName = 'OptimizedTaskCard';

/**
 * 虚拟化行组件
 * 用于虚拟滚动中的行渲染
 */
interface VirtualizedRowProps {
  index: number;
  tasks: Task[];
  vehicles: Vehicle[];
  config: TaskCardConfig;
  size?: 'small' | 'medium' | 'large' | 'extra-large';
  columnsPerRow: number;
  vehicleDisplayMode?: VehicleDisplayMode;
  inTaskVehicleCardStyles?: InTaskVehicleCardStyle;
  density?: 'compact' | 'normal' | 'loose';
  onTaskContextMenu?: (event: React.MouseEvent, task: Task) => void;
  onTaskDoubleClick?: (task: Task) => void;
  onDropVehicleOnLine?: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  onCancelDispatch?: (vehicleId: string) => void;
  onOpenStyleEditor?: () => void;
  onOpenDeliveryOrderDetails?: (vehicleId: string, taskId: string) => void;
  onOpenVehicleContextMenu?: (event: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
  gridColumns: string;
  spacingClass: string;
}

export const VirtualizedRow = memo<VirtualizedRowProps>(({
  index,
  tasks,
  vehicles,
  config,
  size = 'medium',
  columnsPerRow,
  vehicleDisplayMode,
  inTaskVehicleCardStyles,
  density,
  onTaskContextMenu,
  onTaskDoubleClick,
  onDropVehicleOnLine,
  onCancelDispatch,
  onOpenStyleEditor,
  onOpenDeliveryOrderDetails,
  onOpenVehicleContextMenu,
  gridColumns,
  spacingClass
}) => {
  // 计算当前行的任务
  const rowTasks = useMemo(() => {
    const startIndex = index * columnsPerRow;
    const endIndex = Math.min(startIndex + columnsPerRow, tasks.length);
    return tasks.slice(startIndex, endIndex);
  }, [index, columnsPerRow, tasks]);

  // 缓存车辆分组
  const vehiclesByTask = useMemo(() => {
    const map = new Map<string, Vehicle[]>();
    vehicles.forEach(vehicle => {
      if (vehicle.assignedTaskId) {
        if (!map.has(vehicle.assignedTaskId)) {
          map.set(vehicle.assignedTaskId, []);
        }
        map.get(vehicle.assignedTaskId)!.push(vehicle);
      }
    });
    return map;
  }, [vehicles]);

  return (
    <div
      className={`grid w-full ${gridColumns} ${spacingClass} virtual-row-performance`}
      style={{
        minHeight: '420px', // 固定高度
        transform: 'translateZ(0)', // 启用硬件加速
        contain: 'layout style paint', // CSS 包含优化
      }}
    >
      {rowTasks.map((task) => {
        const taskVehicles = vehiclesByTask.get(task.id) || [];

        return (
          <OptimizedTaskCard
            key={task.id}
            task={task}
            vehicles={taskVehicles}
            config={config}
            size={size}
            vehicleDisplayMode={vehicleDisplayMode}
            inTaskVehicleCardStyles={inTaskVehicleCardStyles}
            density={density}
            onTaskContextMenu={onTaskContextMenu}
            onTaskDoubleClick={onTaskDoubleClick}
            onDropVehicleOnLine={onDropVehicleOnLine}
            onCancelDispatch={onCancelDispatch}
            onOpenStyleEditor={onOpenStyleEditor}
            onOpenDeliveryOrderDetails={onOpenDeliveryOrderDetails}
            onOpenVehicleContextMenu={onOpenVehicleContextMenu}
          />
        );
      })}
    </div>
  );
}, (prevProps, nextProps) => {
  // 优化的比较函数
  return (
    prevProps.index === nextProps.index &&
    prevProps.tasks.length === nextProps.tasks.length &&
    prevProps.vehicles.length === nextProps.vehicles.length &&
    prevProps.config === nextProps.config &&
    prevProps.columnsPerRow === nextProps.columnsPerRow &&
    prevProps.vehicleDisplayMode === nextProps.vehicleDisplayMode &&
    prevProps.density === nextProps.density &&
    prevProps.gridColumns === nextProps.gridColumns &&
    prevProps.spacingClass === nextProps.spacingClass
  );
});

VirtualizedRow.displayName = 'VirtualizedRow';

/**
 * 懒加载卡片组件
 * 使用 Intersection Observer 实现懒加载
 */
interface LazyTaskCardProps extends OptimizedTaskCardProps {
  isVisible?: boolean;
}

export const LazyTaskCard = memo<LazyTaskCardProps>(({
  isVisible = true,
  ...props
}) => {
  // 如果不可见，返回占位符
  if (!isVisible) {
    return (
      <div 
        className="h-[420px] w-[350px] bg-muted/30 rounded-lg animate-pulse"
        style={{
          contain: 'layout style paint',
        }}
      />
    );
  }

  return <OptimizedTaskCard {...props} />;
}, (prevProps, nextProps) => {
  return (
    prevProps.isVisible === nextProps.isVisible &&
    (prevProps.isVisible === false || (
      prevProps.task.id === nextProps.task.id &&
      prevProps.vehicles.length === nextProps.vehicles.length &&
      prevProps.config === nextProps.config
    ))
  );
});

LazyTaskCard.displayName = 'LazyTaskCard';
