'use client';

import React, { useMemo, useState ,useEffect} from 'react';
import { useDrop } from 'react-dnd';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import {
  Clock,
  MapPin,
  Building2,
  Truck,
  AlertTriangle,
  CheckCircle2,
  Pause,
  XCircle,
  Zap,
  Target,
  Factory
} from 'lucide-react';
import type { Task, Vehicle } from '@/types';
import { ItemTypes } from '@/constants/dndItemTypes';

/**
 * 默认任务卡片组件属性
 */
interface DefaultTaskCardProps {
  task: Task;
  vehicles: Vehicle[];
  className?: string;
  onTaskContextMenu?: (event: React.MouseEvent, task: Task) => void;
  onTaskDoubleClick?: (task: Task) => void;
  onDropVehicleOnLine?: (vehicle: Vehicle, taskId: string, lineId: string) => void;
}

/**
 * 获取状态图标和样式
 */
const getStatusInfo = (status: string) => {
  switch (status) {
    case 'ReadyToProduce':
      return { icon: Pause, label: '待生产', className: 'bg-yellow-100 text-yellow-700 border-yellow-200' };
    case 'InProgress':
      return { icon: Clock, label: '进行中', className: 'bg-blue-100 text-blue-700 border-blue-200' };
    case 'Completed':
      return { icon: CheckCircle2, label: '已完成', className: 'bg-green-100 text-green-700 border-green-200' };
    case 'Cancelled':
      return { icon: XCircle, label: '已取消', className: 'bg-red-100 text-red-700 border-red-200' };
    default:
      return { icon: AlertTriangle, label: '未知', className: 'bg-gray-100 text-gray-700 border-gray-200' };
  }
};

/**
 * 环形进度组件
 */
const CircularProgress: React.FC<{ percentage: number; size?: 'small' | 'medium' }> = ({ 
  percentage, 
  size = 'small' 
}) => {
  const sizeClass = size === 'small' ? 'w-8 h-8' : 'w-12 h-12';
  const strokeWidth = size === 'small' ? 2 : 3;
  const radius = size === 'small' ? 14 : 20;
  const circumference = 2 * Math.PI * radius;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  return (
    <div className={cn('relative', sizeClass)}>
      <svg className="transform -rotate-90 w-full h-full">
        <circle
          cx="50%"
          cy="50%"
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="transparent"
          className="text-gray-200"
        />
        <circle
          cx="50%"
          cy="50%"
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          className="text-blue-500 transition-all duration-300"
        />
      </svg>
      <div className="absolute inset-0 flex items-center justify-center">
        <span className={cn(
          'font-medium text-gray-700',
          size === 'small' ? 'text-xs' : 'text-sm'
        )}>
          {percentage}%
        </span>
      </div>
    </div>
  );
};

/**
 * 生产线槽位组件
 */
const ProductionLineSlot: React.FC<{
  lineId: string;
  lineNumber: number;
  task: Task;
  onDropVehicleOnLine?: (vehicle: Vehicle, taskId: string, lineId: string) => void;
}> = ({ lineId, lineNumber, task, onDropVehicleOnLine }) => {
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: ItemTypes.VEHICLE_CARD_DISPATCH,
    drop: (item: any) => {
      if (onDropVehicleOnLine && item.vehicle) {
        onDropVehicleOnLine(item.vehicle, task.id, lineId);
      }
    },
    canDrop: () => task.dispatchStatus === 'InProgress',
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  });

  return (
    <div
      ref={drop}
      className={cn(
        'w-full h-12 rounded-lg border-2 border-dashed transition-all duration-200',
        'flex items-center justify-center text-sm font-medium',
        isOver && canDrop
          ? 'border-primary bg-primary/10 text-primary'
          : 'border-muted-foreground/30 bg-muted/20 text-muted-foreground',
        canDrop && 'hover:border-primary/50 hover:bg-primary/5'
      )}
    >
      <Factory className="w-4 h-4 mr-2" />
      生产线 {lineNumber}
    </div>
  );
};

/**
 * 默认任务卡片组件
 * 实现用户要求的默认配置布局
 */
export const DefaultTaskCard: React.FC<DefaultTaskCardProps> = ({
  task,
  vehicles,
  className,
  onTaskContextMenu,
  onTaskDoubleClick,
  onDropVehicleOnLine
}) => {
  const [showProductionPanel, setShowProductionPanel] = useState(false);

  const statusInfo = getStatusInfo(task.dispatchStatus);
  const StatusIcon = statusInfo.icon;

  // 计算进度百分比
  const progressPercentage = useMemo(() => {
    if (task.requiredVolume === 0) return 0;
    return Math.round((task.completedVolume / task.requiredVolume) * 100);
  }, [task.completedVolume, task.requiredVolume]);

  // 拖拽目标设置
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: ItemTypes.VEHICLE_CARD_DISPATCH,
    drop: (item: any, monitor) => {
      if (monitor.didDrop()) return; // 如果已经被子组件处理，则不处理
      // 这里可以处理直接拖拽到卡片上的逻辑
    },
    canDrop: () => task.dispatchStatus === 'InProgress',
    collect: (monitor) => ({
      isOver: monitor.isOver({ shallow: true }),
      canDrop: monitor.canDrop(),
    }),
  });

  // 当有拖拽悬停时显示生产线面板
  React.useEffect(() => {
    if (isOver && canDrop) {
      setShowProductionPanel(true);
    } else if (!isOver) {
      // 延迟隐藏面板，给用户时间拖拽到生产线
      const timer = setTimeout(() => {
        setShowProductionPanel(false);
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [isOver, canDrop]);

  // 事件处理
  const handleTaskContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    onTaskContextMenu?.(e, task);
  };

  const handleTaskDoubleClick = () => {
    onTaskDoubleClick?.(task);
  };

  return (
    <div className="relative z-0">
      <Card
        ref={drop}
        className={cn(
          'relative overflow-hidden cursor-pointer transition-all duration-200',
          'hover:shadow-md hover:scale-[1.02]',
          'min-h-[320px] max-w-[350px]',
          isOver && canDrop && 'ring-2 ring-primary shadow-lg bg-primary/5',
          className
        )}
        onContextMenu={handleTaskContextMenu}
        onDoubleClick={handleTaskDoubleClick}
      >
      <CardContent className="p-0 h-full flex flex-col">
        {/* 顶部区域 */}
        <div className="flex items-center justify-between p-4 border-b border-border/50">
          {/* 左侧：小号环形进度 + 项目信息 */}
          <div className="flex items-center gap-3 min-w-0 flex-1">
            <CircularProgress percentage={progressPercentage} size="small" />
            <div className="min-w-0 flex-1">
              <div className="font-semibold text-sm truncate">{task.projectName || task.taskNumber}</div>
              <div className="text-xs text-muted-foreground truncate">{task.constructionSite}</div>
            </div>
          </div>
          
          {/* 右侧：发车提醒 + 强度 */}
          <div className="flex flex-col items-end gap-1 flex-shrink-0">
            {task.isDueForDispatch && (
              <Badge className="bg-red-100 text-red-700 border-red-200 text-xs">
                <Zap className="w-3 h-3 mr-1" />
                发车提醒
              </Badge>
            )}
            <Badge className="bg-orange-100 text-orange-700 border-orange-200 text-xs">
              <Target className="w-3 h-3 mr-1" />
              强度
            </Badge>
          </div>
        </div>

        {/* 中间调度车辆区域 */}
        <div className="p-4 border-b border-border/50">
          <div className="flex items-center gap-2 mb-2">
            <Truck className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm font-medium">调度车辆 ({vehicles.length})</span>
          </div>
          <div className="min-h-[60px] bg-muted/30 border border-dashed border-muted-foreground/30 rounded-lg p-2">
            {vehicles.length > 0 ? (
              <div className="flex flex-wrap gap-1">
                {vehicles.map((vehicle) => (
                  <Badge
                    key={vehicle.id}
                    variant="outline"
                    className="text-xs bg-green-50 text-green-700 border-green-200"
                  >
                    {vehicle.vehicleNumber}
                  </Badge>
                ))}
              </div>
            ) : (
              <div className="flex items-center justify-center h-full text-xs text-muted-foreground">
                可拖拽车辆到此区域
              </div>
            )}
          </div>
        </div>

        {/* 中间两列各4个字段 */}
        <div className="flex-1 p-4 border-b border-border/50">
          <div className="grid grid-cols-2 gap-4 h-full">
            {/* 左列 */}
            <div className="space-y-3">
              <div className="text-xs">
                <div className="text-muted-foreground">需求方量</div>
                <div className="font-medium">{task.requiredVolume}m³</div>
              </div>
              <div className="text-xs">
                <div className="text-muted-foreground">完成方量</div>
                <div className="font-medium">{task.completedVolume}m³</div>
              </div>
              <div className="text-xs">
                <div className="text-muted-foreground">计划时间</div>
                <div className="font-medium">{task.scheduledTime || '--'}</div>
              </div>
              <div className="text-xs">
                <div className="text-muted-foreground">联系电话</div>
                <div className="font-medium">{task.contactPhone || '--'}</div>
              </div>
            </div>
            
            {/* 右列 */}
            <div className="space-y-3">
              <div className="text-xs">
                <div className="text-muted-foreground">完成进度</div>
                <div className="font-medium">{progressPercentage}%</div>
              </div>
              <div className="text-xs">
                <div className="text-muted-foreground">预计时长</div>
                <div className="font-medium">--</div>
              </div>
              <div className="text-xs">
                <div className="text-muted-foreground">施工地点</div>
                <div className="font-medium truncate">{task.constructionSite}</div>
              </div>
              <div className="text-xs">
                <div className="text-muted-foreground">状态</div>
                <Badge className={cn('text-xs', statusInfo.className)}>
                  <StatusIcon className="w-3 h-3 mr-1" />
                  {statusInfo.label}
                </Badge>
              </div>
            </div>
          </div>
        </div>

        {/* 底部两列各2个字段 */}
        <div className="p-4">
          <div className="grid grid-cols-2 gap-4">
            {/* 左列 */}
            <div className="space-y-2">
              <div className="text-xs">
                <div className="text-muted-foreground">客户名称</div>
                <div className="font-medium truncate">{task.customerName}</div>
              </div>
              <div className="text-xs">
                <div className="text-muted-foreground">创建时间</div>
                <div className="font-medium">{task.createdAt || '--'}</div>
              </div>
            </div>
            
            {/* 右列 */}
            <div className="space-y-2">
              <div className="text-xs">
                <div className="text-muted-foreground">任务编号</div>
                <div className="font-medium truncate">{task.taskNumber}</div>
              </div>
              <div className="text-xs">
                <div className="text-muted-foreground">更新时间</div>
                <div className="font-medium">{task.createdAt || '--'}</div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    {/* 生产线面板 */}
    {showProductionPanel && (
      <div
        className={cn(
          'absolute top-0 right-0 h-full w-48 bg-background/95 backdrop-blur-sm',
          'border-l border-border shadow-lg rounded-r-lg',
          'transform transition-all duration-300 ease-out',
          'translate-x-full',
          showProductionPanel && 'translate-x-0'
        )}
        style={{ zIndex: 10 }}
      >
        <div className="p-4 h-full flex flex-col">
          <div className="flex items-center gap-2 mb-4 pb-2 border-b">
            <Factory className="w-4 h-4 text-primary" />
            <span className="font-medium text-sm">选择生产线</span>
          </div>

          <div className="flex-1 space-y-3 overflow-y-auto">
            {Array.from({ length: task.productionLineCount || 3 }).map((_, index) => (
              <ProductionLineSlot
                key={index}
                lineId={`L${index + 1}`}
                lineNumber={index + 1}
                task={task}
                onDropVehicleOnLine={onDropVehicleOnLine}
              />
            ))}
          </div>

          <div className="mt-4 pt-2 border-t text-xs text-muted-foreground">
            拖拽车辆到生产线完成发车
          </div>
        </div>
      </div>
    )}
  </div>
  );
};
