'use client';

import React, { useMemo, useState } from 'react';
import { useDrop } from 'react-dnd';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import {
  Clock,
  MapPin,
  Building2,
  Truck,
  AlertTriangle,
  CheckCircle2,
  Pause,
  XCircle,

  Target,
  Factory
} from 'lucide-react';
import { differenceInSeconds } from 'date-fns';
import type { Task, Vehicle, VehicleDisplayMode, InTaskVehicleCardStyle } from '@/types';
import { ItemTypes } from '@/constants/dndItemTypes';
import { InTaskVehicleCard } from '../in-task-vehicle-card';

/**
 * 默认任务卡片组件属性
 */
interface DefaultTaskCardProps {
  task: Task;
  vehicles: Vehicle[];
  className?: string;
  vehicleDisplayMode?: VehicleDisplayMode;
  inTaskVehicleCardStyles?: InTaskVehicleCardStyle;
  density?: 'compact' | 'normal' | 'loose';
  onTaskContextMenu?: (event: React.MouseEvent, task: Task) => void;
  onTaskDoubleClick?: (task: Task) => void;
  onDropVehicleOnLine?: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  onCancelDispatch?: (vehicleId: string) => void;
  onOpenStyleEditor?: () => void;
  onOpenDeliveryOrderDetails?: (vehicleId: string, taskId: string) => void;
  onOpenVehicleContextMenu?: (event: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
}

/**
 * 获取状态图标和样式
 */
const getStatusInfo = (status: string) => {
  switch (status) {
    case 'ReadyToProduce':
      return { icon: Pause, label: '待生产', className: 'bg-yellow-100 text-yellow-700 border-yellow-200' };
    case 'InProgress':
      return { icon: Clock, label: '进行中', className: 'bg-blue-100 text-blue-700 border-blue-200' };
    case 'Completed':
      return { icon: CheckCircle2, label: '已完成', className: 'bg-green-100 text-green-700 border-green-200' };
    case 'Cancelled':
      return { icon: XCircle, label: '已取消', className: 'bg-red-100 text-red-700 border-red-200' };
    default:
      return { icon: AlertTriangle, label: '未知', className: 'bg-gray-100 text-gray-700 border-gray-200' };
  }
};

/**
 * 环形进度组件
 */
const CircularProgress: React.FC<{ percentage: number; size?: 'small' | 'medium' }> = ({ 
  percentage, 
  size = 'small' 
}) => {
  const sizeClass = size === 'small' ? 'w-8 h-8' : 'w-12 h-12';
  const strokeWidth = size === 'small' ? 2 : 3;
  const radius = size === 'small' ? 14 : 20;
  const circumference = 2 * Math.PI * radius;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  return (
    <div className={cn('relative', sizeClass)}>
      <svg className="transform -rotate-90 w-full h-full">
        <circle
          cx="50%"
          cy="50%"
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="transparent"
          className="text-gray-200"
        />
        <circle
          cx="50%"
          cy="50%"
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          className="text-blue-500 transition-all duration-300"
        />
      </svg>
      <div className="absolute inset-0 flex items-center justify-center">
        <span className={cn(
          'font-medium text-gray-700',
          size === 'small' ? 'text-xs' : 'text-sm'
        )}>
          {percentage}%
        </span>
      </div>
    </div>
  );
};

/**
 * 生产线槽位组件
 */
const ProductionLineSlot: React.FC<{
  lineId: string;
  lineNumber: number;
  task: Task;
  onDropVehicleOnLine?: (vehicle: Vehicle, taskId: string, lineId: string) => void;
}> = ({ lineId, lineNumber, task, onDropVehicleOnLine }) => {
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: ItemTypes.VEHICLE_CARD_DISPATCH,
    drop: (item: any) => {
      if (onDropVehicleOnLine && item.vehicle) {
        onDropVehicleOnLine(item.vehicle, task.id, lineId);
      }
    },
    canDrop: () => task.dispatchStatus === 'InProgress',
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  });

  return (
    <div
      ref={drop as any}
      className={cn(
        'w-full h-12 rounded-lg border-2 border-dashed transition-all duration-200',
        'flex items-center justify-center text-sm font-medium',
        isOver && canDrop
          ? 'border-primary bg-primary/10 text-primary'
          : 'border-muted-foreground/30 bg-muted/20 text-muted-foreground',
        canDrop && 'hover:border-primary/50 hover:bg-primary/5'
      )}
    >
      <Factory className="w-4 h-4 mr-2" />
      生产线 {lineNumber}
    </div>
  );
};

/**
 * 默认任务卡片组件
 * 实现用户要求的默认配置布局
 */
export const DefaultTaskCard: React.FC<DefaultTaskCardProps> = ({
  task,
  vehicles,
  className,
  vehicleDisplayMode = 'compact' as VehicleDisplayMode,
  inTaskVehicleCardStyles = {
    cardWidth: 'w-14',
    cardHeight: 'h-8',
    fontSize: 'text-[10px]',
    fontColor: 'text-foreground',
    vehicleNumberFontWeight: 'font-medium',
    cardBgColor: 'bg-background',
    statusDotSize: 'w-1 h-1',
    borderRadius: 'rounded-sm',
    boxShadow: 'shadow-sm'
  },
  density = 'normal',
  onTaskContextMenu,
  onTaskDoubleClick,
  onDropVehicleOnLine,
  onCancelDispatch,
  onOpenStyleEditor,
  onOpenDeliveryOrderDetails,
  onOpenVehicleContextMenu
}) => {
  const [showProductionPanel, setShowProductionPanel] = useState(false);

  const statusInfo = getStatusInfo(task.dispatchStatus);
  const StatusIcon = statusInfo.icon;

  // 计算进度百分比
  const progressPercentage = useMemo(() => {
    if (task.requiredVolume === 0) return 0;
    return Math.round((task.completedVolume / task.requiredVolume) * 100);
  }, [task.completedVolume, task.requiredVolume]);

  // 计算发车提醒信息
  const dispatchReminderInfo = useMemo(() => {
    // 如果任务不在进行中状态，不显示提醒
    if (task.dispatchStatus !== 'InProgress') {
      return null;
    }

    // 如果没有设置发车频率，不显示提醒
    if (!task.dispatchFrequencyMinutes || task.dispatchFrequencyMinutes <= 0) {
      return null;
    }

    // 如果没有下次发车时间，不显示提醒
    if (!task.nextScheduledDispatchTime) {
      return null;
    }

    const now = new Date();
    const nextDispatchTime = new Date(task.nextScheduledDispatchTime);
    const totalSecondsDiff = differenceInSeconds(nextDispatchTime, now);

    // 如果已经过了发车时间
    if (totalSecondsDiff <= 0) {
      return {
        text: '立即发车',
        type: 'urgent' as const,
        icon: AlertTriangle
      };
    }

    const minutesDiff = Math.floor(totalSecondsDiff / 60);
    const secondsDiff = totalSecondsDiff % 60;

    // 根据时间差确定显示类型和样式
    if (minutesDiff <= 5) {
      return {
        text: `${minutesDiff}:${secondsDiff.toString().padStart(2, '0')}`,
        type: 'urgent' as const,
        icon: AlertTriangle
      };
    } else if (minutesDiff <= 15) {
      return {
        text: `距发:${minutesDiff}分钟`,
        type: 'warning' as const,
        icon: Clock
      };
    } else {
      return {
        text: `距发:${minutesDiff}分钟`,
        type: 'normal' as const,
        icon: Clock
      };
    }
  }, [task.dispatchStatus, task.dispatchFrequencyMinutes, task.nextScheduledDispatchTime]);

  // 拖拽目标设置
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: ItemTypes.VEHICLE_CARD_DISPATCH,
    drop: (item: any, monitor) => {
      if (monitor.didDrop()) return; // 如果已经被子组件处理，则不处理
      // 这里可以处理直接拖拽到卡片上的逻辑
    },
    canDrop: () => task.dispatchStatus === 'InProgress',
    collect: (monitor) => ({
      isOver: monitor.isOver({ shallow: true }),
      canDrop: monitor.canDrop(),
    }),
  });

  // 当有拖拽悬停时显示生产线面板 - 使用防抖优化
  React.useEffect(() => {
    if (isOver && canDrop) {
      setShowProductionPanel(true);
    } else if (!isOver) {
      // 延迟隐藏面板，给用户时间拖拽到生产线，同时减少闪烁
      const timer = setTimeout(() => {
        setShowProductionPanel(false);
      }, 150); // 减少延迟时间
      return () => clearTimeout(timer);
    }
  }, [isOver, canDrop]);

  // 事件处理
  const handleTaskContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    onTaskContextMenu?.(e, task);
  };

  const handleTaskDoubleClick = () => {
    onTaskDoubleClick?.(task);
  };

  // 缓存拖拽样式，减少重新计算
  const dragStyles = React.useMemo(() => {
    if (isOver && canDrop) {
      return 'ring-2 ring-primary shadow-lg bg-primary/5';
    }
    return '';
  }, [isOver, canDrop]);

  return (
    <div className="relative z-0" ref={drop as any}>
      <Card
        className={cn(
          'relative overflow-hidden cursor-pointer',
          'transition-all duration-150 ease-out', // 减少动画时间，使用ease-out
          'hover:shadow-md hover:scale-[1.01]', // 减少缩放幅度
          'h-[420px] w-[350px]', // 固定高度和宽度
          dragStyles,
          className
        )}
        onContextMenu={handleTaskContextMenu}
        onDoubleClick={handleTaskDoubleClick}
      >
      <CardContent className="p-2 h-full flex flex-col space-y-2">
        {/* 顶部区域 */}
        <div className="flex items-center justify-between border-b border-border/50">
          {/* 左侧：小号环形进度 + 项目信息 */}
          <div className="flex items-center gap-3 min-w-0 flex-1">
            <CircularProgress percentage={progressPercentage} size="small" />
            <div className="min-w-0 flex-1">
              <div className="font-semibold text-sm truncate">{task.projectName || task.taskNumber}</div>
              <div className="text-xs text-muted-foreground truncate">{task.constructionSite}</div>
            </div>
          </div>
          
          {/* 右侧：发车提醒 + 强度 */}
          <div className="flex flex-col items-end gap-1 flex-shrink-0">
            {dispatchReminderInfo && (
              <Badge
                className={cn(
                  'text-xs font-bold border-2 cursor-help',
                  dispatchReminderInfo.type === 'urgent'
                    ? 'bg-destructive/20 text-destructive border-destructive animate-pulse shadow-lg'
                    : dispatchReminderInfo.type === 'warning'
                    ? 'bg-warning/20 text-warning border-warning shadow-md'
                    : 'bg-muted/20 text-muted-foreground border-muted'
                )}
                title={`下次发车时间: ${task.nextScheduledDispatchTime ? new Date(task.nextScheduledDispatchTime).toLocaleString() : '未计划'}`}
              >
                {React.createElement(dispatchReminderInfo.icon, { className: "w-3 h-3 mr-1" })}
                {dispatchReminderInfo.text}
              </Badge>
            )}
            <Badge className="bg-orange-100 text-orange-700 border-orange-200 text-xs">
              <Target className="w-3 h-3 mr-1" />
              {task.strength}
            </Badge>
          </div>
        </div>

        {/* 中间调度车辆区域 */}
        <div className="border-b border-border/50">
          <div className="flex items-center gap-2 mb-2">
            <Truck className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm font-medium">调度车辆 ({vehicles.length})</span>
          </div>
          <div className="h-[80px] bg-muted/30 border border-dashed border-muted-foreground/30 rounded-lg p-2 overflow-hidden">
            {vehicles.length > 0 ? (
              <div className="h-full overflow-x-auto overflow-y-hidden">
                <div className="flex flex-wrap gap-1 h-full content-start" style={{ minWidth: 'max-content' }}>
                  {vehicles.map((vehicle) => (
                    <InTaskVehicleCard
                      key={vehicle.id}
                      vehicle={vehicle}
                      task={task}
                      vehicleDisplayMode={vehicleDisplayMode}
                      inTaskVehicleCardStyles={inTaskVehicleCardStyles}
                      productionLineCount={task.productionLineCount || 3}
                      density={density}
                      onCancelDispatch={onCancelDispatch}
                      onOpenStyleEditor={onOpenStyleEditor}
                      onOpenDeliveryOrderDetails={(vehicleId) => onOpenDeliveryOrderDetails?.(vehicleId, task.id)}
                      onOpenContextMenu={(e, vehicle) => onOpenVehicleContextMenu?.(e, vehicle, task)}
                    />
                  ))}
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-full text-xs text-muted-foreground">
                可拖拽车辆到此区域
              </div>
            )}
          </div>
        </div>

        {/* 中间两列各4个字段 */}
        <div className="flex-1 border-b border-border/50">
          <div className="grid grid-cols-2 gap-4 h-full">
            {/* 左列 */}
            <div className="space-y-3">
              <div className="text-xs">
                <div className="text-muted-foreground">需求方量</div>
                <div className="font-medium">{task.requiredVolume}m³</div>
              </div>
              <div className="text-xs">
                <div className="text-muted-foreground">完成方量</div>
                <div className="font-medium">{task.completedVolume}m³</div>
              </div>
              <div className="text-xs">
                <div className="text-muted-foreground">计划时间</div>
                <div className="font-medium">{task.scheduledTime || '--'}</div>
              </div>
              <div className="text-xs">
                <div className="text-muted-foreground">联系电话</div>
                <div className="font-medium">{task.contactPhone || '--'}</div>
              </div>
            </div>
            
            {/* 右列 */}
            <div className="space-y-3">
              <div className="text-xs">
                <div className="text-muted-foreground">完成进度</div>
                <div className="font-medium">{progressPercentage}%</div>
              </div>
              <div className="text-xs">
                <div className="text-muted-foreground">预计时长</div>
                <div className="font-medium">--</div>
              </div>
              <div className="text-xs">
                <div className="text-muted-foreground">施工地点</div>
                <div className="font-medium truncate">{task.constructionSite}</div>
              </div>
              <div className="text-xs">
                <div className="text-muted-foreground">状态</div>
                <Badge className={cn('text-xs', statusInfo.className)}>
                  <StatusIcon className="w-3 h-3 mr-1" />
                  {statusInfo.label}
                </Badge>
              </div>
            </div>
          </div>
        </div>

        {/* 底部两列各2个字段 */}
        <div>
          <div className="grid grid-cols-2 gap-4">
            {/* 左列 */}
            <div className="space-y-2">
              <div className="text-xs">
                <div className="text-muted-foreground">客户名称</div>
                <div className="font-medium truncate">{task.customerName}</div>
              </div>
              <div className="text-xs">
                <div className="text-muted-foreground">创建时间</div>
                <div className="font-medium">{task.createdAt || '--'}</div>
              </div>
            </div>
            
            {/* 右列 */}
            <div className="space-y-2">
              <div className="text-xs">
                <div className="text-muted-foreground">任务编号</div>
                <div className="font-medium truncate">{task.taskNumber}</div>
              </div>
              <div className="text-xs">
                <div className="text-muted-foreground">更新时间</div>
                <div className="font-medium">{task.createdAt || '--'}</div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    {/* 生产线面板 */}
    {showProductionPanel && (
      <div
        className={cn(
          'absolute top-0 right-0 h-full w-48 bg-background/95 backdrop-blur-sm',
          'border-l border-border shadow-lg rounded-r-lg',
          'transform transition-all duration-300 ease-out',
          'translate-x-full',
          showProductionPanel && 'translate-x-0'
        )}
        style={{ zIndex: 10 }}
      >
        <div className="p-4 h-full flex flex-col">
          <div className="flex items-center gap-2 mb-4 pb-2 border-b">
            <Factory className="w-4 h-4 text-primary" />
            <span className="font-medium text-sm">选择生产线</span>
          </div>

          <div className="flex-1 space-y-3 overflow-y-auto">
            {Array.from({ length: task.productionLineCount || 3 }).map((_, index) => (
              <ProductionLineSlot
                key={index}
                lineId={`L${index + 1}`}
                lineNumber={index + 1}
                task={task}
                onDropVehicleOnLine={onDropVehicleOnLine}
              />
            ))}
          </div>

          <div className="mt-4 pt-2 border-t text-xs text-muted-foreground">
            拖拽车辆到生产线完成发车
          </div>
        </div>
      </div>
    )}
  </div>
  );
};
