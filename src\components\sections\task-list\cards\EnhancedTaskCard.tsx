// src/components/sections/task-list/cards/EnhancedTaskCard.tsx
'use client';

import React, { useMemo } from 'react';
import { useDrop, useDrag } from 'react-dnd';
import { cn } from '@/lib/utils';
// Removed useDragDropContext as we're using react-dnd directly
// Removed DroppableVehicleList as it's obsolete
import { TaskStatusBadge } from './TaskStatusBadge';
import { TaskProgressRing } from './TaskProgressRing';
import { TaskInfoSection } from './TaskInfoSection';
import { DispatchReminderBadge } from './DispatchReminderBadge';
import { InTaskVehicleCard } from '../in-task-vehicle-card'; // Corrected path
import { 
  TruckIcon,
  AlertTriangle,
  CheckCircle2,
  XCircle,
  Pause,
  Clock
} from 'lucide-react';
import type { Task, Vehicle, VehicleDisplayMode } from '@/types';
import { ItemTypes } from '@/constants/dndItemTypes';

interface CardConfig {
  content: any;
  size: 'small' | 'medium' | 'large' | 'extra-large';
  layout: 'compact' | 'standard' | 'detailed' | 'minimal';
  theme: 'default' | 'modern' | 'glass' | 'gradient' | 'dark';
  spacing: 'tight' | 'normal' | 'loose';
  borderRadius: 'none' | 'small' | 'medium' | 'large' | 'full';
  shadow: 'none' | 'small' | 'medium' | 'large' | 'glow';
  animation: 'none' | 'subtle' | 'smooth' | 'bouncy';
  columns: 'auto' | '1' | '2' | '3' | '4' | '5' | '6';
}

interface EnhancedTaskCardProps {
  task: Task;
  vehicles: Vehicle[]; // All vehicles assigned to this task
  config: CardConfig;
  vehicleDisplayMode: VehicleDisplayMode;
  inTaskVehicleCardStyles?: any;
  density?: 'compact' | 'normal' | 'loose';
  onCancelDispatch: (vehicleId: string) => void;
  onOpenStyleEditor: () => void;
  onOpenDeliveryOrderDetails: (vehicleId: string, taskId: string) => void;
  onOpenContextMenu: (e: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
  onTaskContextMenu: (e: React.MouseEvent, task: Task) => void;
  onTaskDoubleClick: (task: Task) => void;
  // Added for react-dnd integration, called when a vehicle from dispatch panel is dropped on this card
  onDropVehicleFromPanelOnTaskCard?: (vehicle: Vehicle, taskId: string) => void; 
}

interface DraggableInTaskVehicleForEnhancedProps {
  vehicle: Vehicle;
  task: Task;
  index: number; // index for reordering (if implemented)
  // Props for InTaskVehicleCard
  vehicleDisplayMode: VehicleDisplayMode;
  inTaskVehicleCardStyles?: any;
  productionLineCount: number; // task.productionLineCount || 1
  density?: 'compact' | 'normal' | 'loose';
  onCancelDispatch: (vehicleId: string) => void;
  onOpenStyleEditor: () => void;
  onOpenDeliveryOrderDetails: (vehicleId: string, taskId: string) => void;
  onOpenContextMenu: (e: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
}

const DraggableInTaskVehicleForEnhanced: React.FC<DraggableInTaskVehicleForEnhancedProps> = ({
  vehicle,
  task,
  index,
  ...inTaskCardProps
}) => {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: ItemTypes.IN_TASK_VEHICLE,
    item: { 
      vehicle, 
      index, 
      sourceTaskId: task.id, 
      sourceProductionLineId: vehicle.assignedProductionLineId,
      type: ItemTypes.IN_TASK_VEHICLE, // Explicitly set type
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }));

  return (
    <div ref={drag} style={{ opacity: isDragging ? 0.5 : 1 }}>
      <InTaskVehicleCard
        // 显式传递task参数,确保其为必选且非undefined
        task={task as Task}
        // 确保task参数为必选项,不能为undefined
        inTaskVehicleCardStyles={{}} // 确保提供默认的样式对象以满足必选属性要求
        vehicle={vehicle}
        {...inTaskCardProps}
        isDragging={isDragging}
      />
    </div>
  );
};


export const EnhancedTaskCard: React.FC<EnhancedTaskCardProps> = React.memo(({
  task,
  vehicles,
  config,
  vehicleDisplayMode,
  inTaskVehicleCardStyles,
  density = 'normal',
  onCancelDispatch,
  onOpenStyleEditor,
  onOpenDeliveryOrderDetails,
  onOpenContextMenu,
  onTaskContextMenu,
  onTaskDoubleClick,
  onDropVehicleFromPanelOnTaskCard,
}) => {
  // Main card area drop target
  const [{ isOver: isOverCard, canDrop: canDropOnCard }, dropCardRef] = useDrop({
    accept: ItemTypes.VEHICLE_CARD_DISPATCH,
    drop: (item: any, monitor) => {
      if (monitor.didDrop()) return; 
      if (onDropVehicleFromPanelOnTaskCard && task.dispatchStatus === 'InProgress') {
        onDropVehicleFromPanelOnTaskCard(item.vehicle, task.id);
      }
    },
    canDrop: (item, monitor) => task.dispatchStatus === 'InProgress',
    collect: (monitor) => ({
      isOver: monitor.isOver({ shallow: true }),
      canDrop: monitor.canDrop(),
    }),
  });

  // Vehicle list area drop target
  const [{ isOver: isOverVehicleArea, canDrop: canDropOnVehicleArea }, dropVehicleAreaRef] = useDrop({
    accept: [ItemTypes.VEHICLE_CARD_DISPATCH, ItemTypes.IN_TASK_VEHICLE],
    drop: (item: any, monitor) => {
      if (monitor.didDrop()) return;
      if (item.type === ItemTypes.VEHICLE_CARD_DISPATCH && onDropVehicleFromPanelOnTaskCard && task.dispatchStatus === 'InProgress') {
        onDropVehicleFromPanelOnTaskCard(item.vehicle, task.id);
      } else if (item.type === ItemTypes.IN_TASK_VEHICLE) {
        // Placeholder for reordering or moving from another task.
        // For now, focuses on receiving from dispatch panel.
        console.log("IN_TASK_VEHICLE dropped in EnhancedTaskCard vehicle area - implement logic if needed");
      }
    },
    canDrop: (item, monitor) => task.dispatchStatus === 'InProgress',
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  });

  const cardSizeStyles = useMemo(() => {
    switch (config.size) {
      case 'small': return 'min-h-[200px] max-h-[250px]';
      case 'large': return 'min-h-[350px] max-h-[450px]';
      case 'extra-large': return 'min-h-[450px] max-h-[600px]';
      default: return 'min-h-[280px] max-h-[350px]';
    }
  }, [config.size]);

  const themeStyles = useMemo(() => {
    const baseStyles = "transition-all duration-300 ease-in-out";
    switch (config.theme) {
      case 'modern': return cn(baseStyles, "bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900", "border border-gray-200/50 dark:border-gray-700/50");
      case 'glass': return cn(baseStyles, "bg-white/80 backdrop-blur-md border border-white/20", "dark:bg-gray-900/80 dark:border-gray-700/20");
      case 'gradient': return cn(baseStyles, "bg-gradient-to-br from-blue-50 via-white to-purple-50", "dark:from-blue-900/20 dark:via-gray-900 dark:to-purple-900/20", "border border-blue-200/30 dark:border-blue-700/30");
      case 'dark': return cn(baseStyles, "bg-gray-900 text-white border border-gray-700");
      default: return cn(baseStyles, "bg-card text-card-foreground border border-border");
    }
  }, [config.theme]);

  const borderRadiusStyles = useMemo(() => {
    switch (config.borderRadius) {
      case 'none': return 'rounded-none'; case 'small': return 'rounded-sm';
      case 'large': return 'rounded-xl'; case 'full': return 'rounded-3xl';
      default: return 'rounded-lg';
    }
  }, [config.borderRadius]);

  const shadowStyles = useMemo(() => {
    switch (config.shadow) {
      case 'none': return ''; case 'small': return 'shadow-sm hover:shadow-md';
      case 'large': return 'shadow-lg hover:shadow-xl';
      case 'glow': return 'shadow-lg shadow-primary/10 hover:shadow-xl hover:shadow-primary/20';
      default: return 'shadow-md hover:shadow-lg';
    }
  }, [config.shadow]);

  const animationStyles = useMemo(() => {
    switch (config.animation) {
      case 'none': return ''; case 'subtle': return 'hover:scale-[1.02] transition-transform duration-200';
      case 'bouncy': return 'hover:scale-105 transition-transform duration-300 ease-out';
      default: return 'hover:scale-[1.03] transition-transform duration-250';
    }
  }, [config.animation]);

  const getDragTargetStyles = () => {
    if (isOverCard && canDropOnCard) { 
      return "ring-2 ring-primary shadow-lg bg-primary/5 scale-[1.02]";
    }
    return "";
  };

  const getStatusIcon = () => {
    switch (task.dispatchStatus) {
      case 'ReadyToProduce': return <Pause className="w-4 h-4 text-yellow-600" />;
      case 'InProgress': return <Clock className="w-4 h-4 text-blue-600" />;
      case 'Completed': return <CheckCircle2 className="w-4 h-4 text-green-600" />;
      case 'Cancelled': return <XCircle className="w-4 h-4 text-red-600" />;
      default: return <AlertTriangle className="w-4 h-4 text-gray-600" />;
    }
  };

  const progressPercentage = useMemo(() => Math.round((task.completedVolume / task.requiredVolume) * 100), [task.completedVolume, task.requiredVolume]);

  const handleTaskContextMenu = (e: React.MouseEvent) => { e.preventDefault(); onTaskContextMenu(e, task); };
  const handleTaskDoubleClick = () => { onTaskDoubleClick(task); };

  // 渲染头部区域
  const renderHeader = () => {
    if (!config.content?.header?.show) return null;
    
    const heightClass = {
      compact: 'p-1',
      normal: 'p-2',
      expanded: 'p-4'
    }[(config.content.header.height || 'normal') as 'compact' | 'normal' | 'expanded'];

    return (
      <div className={cn("flex items-center justify-between border-b border-border/50", heightClass)}>
        <div className="flex items-center gap-3">
          {config.content.header.showStatus && getStatusIcon()}
          <div>
            <h3 className="font-semibold text-lg">{task.taskNumber}</h3>
            <p className="text-sm text-muted-foreground">{task.constructionSite}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {config.content.header.showReminder && task.isDueForDispatch && (
            <DispatchReminderBadge minutes={task.dispatchReminderMinutes || 0} size={config.size} />
          )}
          {config.content.header.showProgress && (
            <TaskStatusBadge status={task.dispatchStatus} size={config.size} />
          )}
        </div>
      </div>
    );
  };

  // 渲染主体区域
  const renderBody = () => {
    if (!config.content?.body?.show) return null;

    const layout = config.content.body.layout || 'info-progress';
    const showTaskInfo = layout === 'info-only' || layout === 'info-progress' || (layout === 'custom' && config.content.body.showTaskInfo);
    const showProgressRing = layout === 'progress-only' || layout === 'info-progress' || (layout === 'custom' && config.content.body.showProgressRing);
    const progressPosition = config.content.body.progressPosition || 'center';

    const progressPositionClass = {
      left: 'justify-start',
      center: 'justify-center',
      right: 'justify-end'
    }[progressPosition];

    return (
      <div className="flex-1 p-4 space-y-4">
        {showTaskInfo && (
          <TaskInfoSection task={task} layout={config.layout} size={config.size} />
        )}
        {showProgressRing && (
          <div className={cn("flex items-center", progressPositionClass)}>
            <TaskProgressRing 
              percentage={progressPercentage} 
              completedVolume={task.completedVolume} 
              requiredVolume={task.requiredVolume} 
              size={config.size} 
            />
          </div>
        )}
      </div>
    );
  };

  // 渲染底部区域
  const renderFooter = () => {
    if (!config.content?.footer?.show) return null;

    const heightClass = {
      compact: 'p-2',
      normal: 'p-4',
      expanded: 'p-6'
    }[config.content.footer.height || 'normal'];

    const vehicleDisplayMode = config.content.footer.vehicleDisplayMode || 'grid';
    const showVehicleList = config.content.footer.showVehicleList !== false;

    if (!showVehicleList) return null;

    return (
      <div className={cn("space-y-3", heightClass)}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <TruckIcon className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm font-medium">分配车辆 ({vehicles.length})</span>
          </div>
        </div>
        <div
          ref={dropVehicleAreaRef} 
          className={cn(
            "min-h-[60px] bg-muted/30 border border-dashed border-muted-foreground/30 rounded-lg",
            "flex flex-wrap gap-2 p-2 transition-all duration-200",
            borderRadiusStyles,
            (isOverVehicleArea && canDropOnVehicleArea) && "bg-primary/10 border-primary/50 scale-[1.02]",
            vehicleDisplayMode === 'list' && "flex-col",
            vehicleDisplayMode === 'compact' && "gap-1"
          )}
        >
          {vehicles.map((vehicle, index) => (
            <DraggableInTaskVehicleForEnhanced
              key={vehicle.id}
              vehicle={vehicle}
              task={task}
              index={index}
              vehicleDisplayMode={vehicleDisplayMode}
              inTaskVehicleCardStyles={inTaskVehicleCardStyles}
              productionLineCount={task.productionLineCount || 1}
              density={density}
              onCancelDispatch={onCancelDispatch}
              onOpenStyleEditor={onOpenStyleEditor}
              onOpenDeliveryOrderDetails={onOpenDeliveryOrderDetails}
              onOpenContextMenu={onOpenContextMenu}
            />
          ))}
          {vehicles.length === 0 && (
            <div className="flex-1 flex items-center justify-center text-xs text-muted-foreground">
              {task.dispatchStatus === 'InProgress' ? "可拖拽车辆到此区域" : "任务未开始"}
            </div>
          )}
        </div>
      </div>
    );
  };

  // 渲染右侧隐藏面板
  const renderSidePanel = () => {
    if (!config.content?.sidePanel?.enabled) return null;

    const width = {
      narrow: 'w-[60px]',
      normal: 'w-[80px]',
      wide: 'w-[120px]'
    }[config.content.sidePanel.width || 'normal'];

    const opacity = config.content.sidePanel.opacity || 0.5;
    const showProductionLines = config.content.sidePanel.showProductionLines !== false;
    const autoExpand = config.content.sidePanel.autoExpand !== false;
    const expandDuration = config.content.sidePanel.expandDuration || 300;

    // 这里可以根据实际需求添加生产线方格的渲染逻辑
    // 目前先显示一个占位区域
    return (
      <div 
        className={cn(
          "absolute top-0 right-0 h-full bg-muted/50 border-l border-border/30",
          "transition-all duration-300 transform translate-x-full",
          width,
          (isOverCard || isOverVehicleArea) && autoExpand && "translate-x-0"
        )}
        style={{ 
          opacity,
          transitionDuration: `${expandDuration}ms`
        }}
      >
        {showProductionLines && (
          <div className="p-2 space-y-1">
            {/* 生产线方格占位 */}
            {Array.from({ length: task.productionLineCount || 3 }).map((_, index) => (
              <div 
                key={index}
                className="w-full h-8 bg-primary/20 rounded border border-primary/30 flex items-center justify-center text-xs"
              >
                {index + 1}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div
      ref={dropCardRef} 
      className={cn(
        "relative flex flex-col overflow-hidden cursor-pointer",
        cardSizeStyles, themeStyles, borderRadiusStyles, shadowStyles, animationStyles,
        getDragTargetStyles(),
        "card-item-performance"
      )}
      onContextMenu={handleTaskContextMenu}
      onDoubleClick={handleTaskDoubleClick}
    >
      {renderHeader()}
      {renderBody()}
      {renderFooter()}
      {renderSidePanel()}
      
      {(isOverCard || isOverVehicleArea) && (canDropOnCard || canDropOnVehicleArea) && (
        <div className="absolute top-2 right-2 z-10">
          <div className="w-3 h-3 bg-primary rounded-full animate-ping" />
          <div className="absolute top-0 right-0 w-3 h-3 bg-primary rounded-full" />
        </div>
      )}
    </div>
  );
});
EnhancedTaskCard.displayName = 'EnhancedTaskCard';
