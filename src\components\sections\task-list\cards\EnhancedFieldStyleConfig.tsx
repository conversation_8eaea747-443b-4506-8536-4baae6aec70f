import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import {
  Eye,
  EyeOff,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Bold,
  Italic,
  Underline,
  Type,
  Palette,
  Move,
  Settings,
  RotateCcw,
  Copy,
  Trash2,
  GripVertical,
} from 'lucide-react';

/**
 * 增强的字段样式配置接口
 */
export interface EnhancedFieldStyle {
  /** 字体大小 */
  fontSize: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl';
  /** 字体粗细 */
  fontWeight: 'normal' | 'medium' | 'semibold' | 'bold';
  /** 文字颜色 */
  color: 'default' | 'muted' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'custom';
  /** 自定义颜色值 */
  customColor?: string;
  /** 文字对齐 */
  textAlign: 'left' | 'center' | 'right';
  /** 是否显示 */
  visible: boolean;
  /** 字段位置 */
  order: number;
  /** 字段宽度 */
  width: 'auto' | 'full' | 'half' | 'third' | 'quarter';
  /** 行高 */
  lineHeight: 'tight' | 'normal' | 'relaxed';
  /** 字母间距 */
  letterSpacing: 'tighter' | 'tight' | 'normal' | 'wide' | 'wider';
  /** 文字装饰 */
  textDecoration: 'none' | 'underline' | 'line-through';
  /** 文字变换 */
  textTransform: 'none' | 'uppercase' | 'lowercase' | 'capitalize';
  /** 边距 */
  margin: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  /** 内边距 */
  padding: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}

/**
 * 增强字段样式配置组件属性
 */
interface EnhancedFieldStyleConfigProps {
  /** 字段标签 */
  label: string;
  /** 字段ID */
  fieldId: string;
  /** 当前样式配置 */
  fieldStyle: EnhancedFieldStyle;
  /** 样式变化回调 */
  onStyleChange: (style: EnhancedFieldStyle) => void;
  /** 字段删除回调 */
  onDelete?: () => void;
  /** 字段复制回调 */
  onCopy?: () => void;
  /** 是否可拖拽 */
  draggable?: boolean;
  /** 拖拽处理器 */
  dragHandleProps?: any;
}

/**
 * 增强的字段样式配置组件
 * 提供更精细的字段样式控制，支持所见即所得的编辑体验
 */
export const EnhancedFieldStyleConfig: React.FC<EnhancedFieldStyleConfigProps> = ({
  label,
  fieldId,
  fieldStyle,
  onStyleChange,
  onDelete,
  onCopy,
  draggable = false,
  dragHandleProps,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');

  /**
   * 更新字段样式
   */
  const updateStyle = (updates: Partial<EnhancedFieldStyle>) => {
    onStyleChange({ ...fieldStyle, ...updates });
  };

  /**
   * 重置样式到默认值
   */
  const resetStyle = () => {
    const defaultStyle: EnhancedFieldStyle = {
      fontSize: 'sm',
      fontWeight: 'normal',
      color: 'default',
      textAlign: 'left',
      visible: true,
      order: 0,
      width: 'auto',
      lineHeight: 'normal',
      letterSpacing: 'normal',
      textDecoration: 'none',
      textTransform: 'none',
      margin: { top: 0, right: 0, bottom: 0, left: 0 },
      padding: { top: 0, right: 0, bottom: 0, left: 0 },
    };
    onStyleChange(defaultStyle);
  };

  /**
   * 颜色选项
   */
  const colorOptions = [
    { value: 'default', label: '默认', preview: '#000000' },
    { value: 'muted', label: '弱化', preview: '#6b7280' },
    { value: 'primary', label: '主色', preview: '#3b82f6' },
    { value: 'secondary', label: '次要', preview: '#64748b' },
    { value: 'success', label: '成功', preview: '#10b981' },
    { value: 'warning', label: '警告', preview: '#f59e0b' },
    { value: 'error', label: '错误', preview: '#ef4444' },
    { value: 'custom', label: '自定义', preview: fieldStyle.customColor || '#000000' },
  ];

  return (
    <div className="border rounded-lg bg-white shadow-sm">
      {/* 字段头部 */}
      <div className="flex items-center justify-between p-3 border-b bg-gray-50/50">
        <div className="flex items-center gap-2">
          {draggable && (
            <div {...dragHandleProps} className="cursor-move text-gray-400 hover:text-gray-600">
              <GripVertical className="w-4 h-4" />
            </div>
          )}
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {fieldId}
            </Badge>
            <Label className="text-sm font-medium">{label}</Label>
          </div>
        </div>
        
        <div className="flex items-center gap-1">
          <Button
            variant={fieldStyle.visible ? 'default' : 'outline'}
            size="sm"
            onClick={() => updateStyle({ visible: !fieldStyle.visible })}
          >
            {fieldStyle.visible ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            <Settings className="w-4 h-4" />
          </Button>
          
          {onCopy && (
            <Button variant="outline" size="sm" onClick={onCopy}>
              <Copy className="w-4 h-4" />
            </Button>
          )}
          
          {onDelete && (
            <Button variant="outline" size="sm" onClick={onDelete}>
              <Trash2 className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>

      {/* 展开的配置区域 */}
      {isExpanded && fieldStyle.visible && (
        <div className="p-4">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">基础</TabsTrigger>
              <TabsTrigger value="typography">字体</TabsTrigger>
              <TabsTrigger value="layout">布局</TabsTrigger>
              <TabsTrigger value="spacing">间距</TabsTrigger>
            </TabsList>

            {/* 基础样式 */}
            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                {/* 字体大小 */}
                <div className="space-y-2">
                  <Label className="text-xs">字体大小</Label>
                  <Select
                    value={fieldStyle.fontSize}
                    onValueChange={(value) => updateStyle({ fontSize: value as any })}
                  >
                    <SelectTrigger className="h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="xs">极小 (12px)</SelectItem>
                      <SelectItem value="sm">小 (14px)</SelectItem>
                      <SelectItem value="base">标准 (16px)</SelectItem>
                      <SelectItem value="lg">大 (18px)</SelectItem>
                      <SelectItem value="xl">特大 (20px)</SelectItem>
                      <SelectItem value="2xl">超大 (24px)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* 字体粗细 */}
                <div className="space-y-2">
                  <Label className="text-xs">字体粗细</Label>
                  <Select
                    value={fieldStyle.fontWeight}
                    onValueChange={(value) => updateStyle({ fontWeight: value as any })}
                  >
                    <SelectTrigger className="h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="normal">正常</SelectItem>
                      <SelectItem value="medium">中等</SelectItem>
                      <SelectItem value="semibold">半粗</SelectItem>
                      <SelectItem value="bold">粗体</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* 文字颜色 */}
              <div className="space-y-2">
                <Label className="text-xs">文字颜色</Label>
                <div className="grid grid-cols-4 gap-2">
                  {colorOptions.map((option) => (
                    <Button
                      key={option.value}
                      variant={fieldStyle.color === option.value ? 'default' : 'outline'}
                      className="h-8 text-xs"
                      onClick={() => updateStyle({ color: option.value as any })}
                    >
                      <div
                        className="w-3 h-3 rounded-full mr-1"
                        style={{ backgroundColor: option.preview }}
                      />
                      {option.label}
                    </Button>
                  ))}
                </div>
                
                {fieldStyle.color === 'custom' && (
                  <Input
                    type="color"
                    value={fieldStyle.customColor || '#000000'}
                    onChange={(e) => updateStyle({ customColor: e.target.value })}
                    className="h-8 w-full"
                  />
                )}
              </div>

              {/* 文字对齐 */}
              <div className="space-y-2">
                <Label className="text-xs">文字对齐</Label>
                <div className="flex gap-1">
                  <Button
                    variant={fieldStyle.textAlign === 'left' ? 'default' : 'outline'}
                    size="sm"
                    className="flex-1"
                    onClick={() => updateStyle({ textAlign: 'left' })}
                  >
                    <AlignLeft className="w-3 h-3" />
                  </Button>
                  <Button
                    variant={fieldStyle.textAlign === 'center' ? 'default' : 'outline'}
                    size="sm"
                    className="flex-1"
                    onClick={() => updateStyle({ textAlign: 'center' })}
                  >
                    <AlignCenter className="w-3 h-3" />
                  </Button>
                  <Button
                    variant={fieldStyle.textAlign === 'right' ? 'default' : 'outline'}
                    size="sm"
                    className="flex-1"
                    onClick={() => updateStyle({ textAlign: 'right' })}
                  >
                    <AlignRight className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            </TabsContent>

            {/* 字体样式 */}
            <TabsContent value="typography" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                {/* 行高 */}
                <div className="space-y-2">
                  <Label className="text-xs">行高</Label>
                  <Select
                    value={fieldStyle.lineHeight}
                    onValueChange={(value) => updateStyle({ lineHeight: value as any })}
                  >
                    <SelectTrigger className="h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="tight">紧密</SelectItem>
                      <SelectItem value="normal">正常</SelectItem>
                      <SelectItem value="relaxed">宽松</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* 字母间距 */}
                <div className="space-y-2">
                  <Label className="text-xs">字母间距</Label>
                  <Select
                    value={fieldStyle.letterSpacing}
                    onValueChange={(value) => updateStyle({ letterSpacing: value as any })}
                  >
                    <SelectTrigger className="h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="tighter">更紧</SelectItem>
                      <SelectItem value="tight">紧密</SelectItem>
                      <SelectItem value="normal">正常</SelectItem>
                      <SelectItem value="wide">宽松</SelectItem>
                      <SelectItem value="wider">更宽</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* 文字装饰 */}
              <div className="space-y-2">
                <Label className="text-xs">文字装饰</Label>
                <Select
                  value={fieldStyle.textDecoration}
                  onValueChange={(value) => updateStyle({ textDecoration: value as any })}
                >
                  <SelectTrigger className="h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">无</SelectItem>
                    <SelectItem value="underline">下划线</SelectItem>
                    <SelectItem value="line-through">删除线</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 文字变换 */}
              <div className="space-y-2">
                <Label className="text-xs">文字变换</Label>
                <Select
                  value={fieldStyle.textTransform}
                  onValueChange={(value) => updateStyle({ textTransform: value as any })}
                >
                  <SelectTrigger className="h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">无</SelectItem>
                    <SelectItem value="uppercase">大写</SelectItem>
                    <SelectItem value="lowercase">小写</SelectItem>
                    <SelectItem value="capitalize">首字母大写</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>

            {/* 布局设置 */}
            <TabsContent value="layout" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                {/* 字段宽度 */}
                <div className="space-y-2">
                  <Label className="text-xs">字段宽度</Label>
                  <Select
                    value={fieldStyle.width}
                    onValueChange={(value) => updateStyle({ width: value as any })}
                  >
                    <SelectTrigger className="h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="auto">自动</SelectItem>
                      <SelectItem value="full">全宽</SelectItem>
                      <SelectItem value="half">半宽</SelectItem>
                      <SelectItem value="third">三分之一</SelectItem>
                      <SelectItem value="quarter">四分之一</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* 排序 */}
                <div className="space-y-2">
                  <Label className="text-xs">排序</Label>
                  <Input
                    type="number"
                    value={fieldStyle.order}
                    onChange={(e) => updateStyle({ order: parseInt(e.target.value) || 0 })}
                    className="h-8"
                    min="0"
                    max="100"
                  />
                </div>
              </div>
            </TabsContent>

            {/* 间距设置 */}
            <TabsContent value="spacing" className="space-y-4">
              {/* 外边距 */}
              <div className="space-y-3">
                <Label className="text-xs">外边距 (px)</Label>
                <div className="grid grid-cols-2 gap-2">
                  <div className="space-y-1">
                    <Label className="text-xs text-muted-foreground">上</Label>
                    <Input
                      type="number"
                      value={fieldStyle.margin.top}
                      onChange={(e) => updateStyle({
                        margin: { ...fieldStyle.margin, top: parseInt(e.target.value) || 0 }
                      })}
                      className="h-7 text-xs"
                      min="0"
                      max="50"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label className="text-xs text-muted-foreground">右</Label>
                    <Input
                      type="number"
                      value={fieldStyle.margin.right}
                      onChange={(e) => updateStyle({
                        margin: { ...fieldStyle.margin, right: parseInt(e.target.value) || 0 }
                      })}
                      className="h-7 text-xs"
                      min="0"
                      max="50"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label className="text-xs text-muted-foreground">下</Label>
                    <Input
                      type="number"
                      value={fieldStyle.margin.bottom}
                      onChange={(e) => updateStyle({
                        margin: { ...fieldStyle.margin, bottom: parseInt(e.target.value) || 0 }
                      })}
                      className="h-7 text-xs"
                      min="0"
                      max="50"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label className="text-xs text-muted-foreground">左</Label>
                    <Input
                      type="number"
                      value={fieldStyle.margin.left}
                      onChange={(e) => updateStyle({
                        margin: { ...fieldStyle.margin, left: parseInt(e.target.value) || 0 }
                      })}
                      className="h-7 text-xs"
                      min="0"
                      max="50"
                    />
                  </div>
                </div>
              </div>

              {/* 内边距 */}
              <div className="space-y-3">
                <Label className="text-xs">内边距 (px)</Label>
                <div className="grid grid-cols-2 gap-2">
                  <div className="space-y-1">
                    <Label className="text-xs text-muted-foreground">上</Label>
                    <Input
                      type="number"
                      value={fieldStyle.padding.top}
                      onChange={(e) => updateStyle({
                        padding: { ...fieldStyle.padding, top: parseInt(e.target.value) || 0 }
                      })}
                      className="h-7 text-xs"
                      min="0"
                      max="50"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label className="text-xs text-muted-foreground">右</Label>
                    <Input
                      type="number"
                      value={fieldStyle.padding.right}
                      onChange={(e) => updateStyle({
                        padding: { ...fieldStyle.padding, right: parseInt(e.target.value) || 0 }
                      })}
                      className="h-7 text-xs"
                      min="0"
                      max="50"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label className="text-xs text-muted-foreground">下</Label>
                    <Input
                      type="number"
                      value={fieldStyle.padding.bottom}
                      onChange={(e) => updateStyle({
                        padding: { ...fieldStyle.padding, bottom: parseInt(e.target.value) || 0 }
                      })}
                      className="h-7 text-xs"
                      min="0"
                      max="50"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label className="text-xs text-muted-foreground">左</Label>
                    <Input
                      type="number"
                      value={fieldStyle.padding.left}
                      onChange={(e) => updateStyle({
                        padding: { ...fieldStyle.padding, left: parseInt(e.target.value) || 0 }
                      })}
                      className="h-7 text-xs"
                      min="0"
                      max="50"
                    />
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <Separator className="my-4" />

          {/* 操作按钮 */}
          <div className="flex justify-between">
            <Button variant="outline" size="sm" onClick={resetStyle}>
              <RotateCcw className="w-4 h-4 mr-1" />
              重置
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export type { EnhancedFieldStyle };
