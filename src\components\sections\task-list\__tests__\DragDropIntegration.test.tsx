/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { DragDropProvider } from '@/contexts/DragDropContext';
import { EnhancedTaskListView } from '../EnhancedTaskListView';
import type { Task, Vehicle, TaskListStoredSettings, VehicleDisplayMode } from '@/types';

// Mock the hooks and stores
jest.mock('@/store/appStore', () => ({
  useAppStore: jest.fn(() => ({
    tasks: [],
    vehicles: [],
    dispatchVehicleToTask: jest.fn(),
    reorderVehiclesInList: jest.fn(),
  })),
}));

jest.mock('@/hooks/use-toast', () => ({
  useToast: jest.fn(() => ({
    toast: jest.fn(),
  })),
}));

// Mock data
const mockTasks: Task[] = [
  {
    id: 'task-1',
    taskNumber: 'T001',
    constructionSite: '测试工地1',
    strength: 'C30',
    dispatchStatus: 'InProgress',
    requiredVolume: 100,
    completedVolume: 50,
    productionLines: ['L1', 'L2'],
  } as Task,
  {
    id: 'task-2',
    taskNumber: 'T002',
    constructionSite: '测试工地2',
    strength: 'C25',
    dispatchStatus: 'Pending',
    requiredVolume: 80,
    completedVolume: 0,
    productionLines: ['L1'],
  } as Task,
];

const mockVehicles: Vehicle[] = [
  {
    id: 'vehicle-1',
    vehicleNumber: 'V001',
    status: 'pending',
    operationalStatus: 'normal',
    currentProductionStatus: 'loading',
  } as Vehicle,
  {
    id: 'vehicle-2',
    vehicleNumber: 'V002',
    status: 'dispatched',
    operationalStatus: 'normal',
    currentProductionStatus: 'loaded',
    assignedTaskId: 'task-1',
  } as Vehicle,
];

const mockSettings: TaskListStoredSettings = {
  density: 'normal',
  listFieldStyles: {},
  listModuleStyles: {},
  listLayoutConfig: {},
  updateSetting: jest.fn(),
} as any;

const defaultProps = {
  filteredTasks: mockTasks,
  vehicles: mockVehicles,
  settings: mockSettings,
  productionLineCount: 2,
  vehicleDisplayMode: 'licensePlate' as VehicleDisplayMode,
  taskStatusFilter: 'all',
  onCancelVehicleDispatch: jest.fn(),
  onOpenDeliveryOrderDetailsForVehicle: jest.fn(),
  onOpenVehicleCardContextMenu: jest.fn(),
  onTaskContextMenu: jest.fn(),
  onTaskDoubleClick: jest.fn(),
  onOpenStyleEditor: jest.fn(),
};

describe('DragDropIntegration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render DragDropProvider without errors', () => {
    render(
      <DragDropProvider>
        <div>Test Content</div>
      </DragDropProvider>
    );

    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('should render EnhancedTaskListView with DragDropProvider', () => {
    render(
      <DragDropProvider>
        <EnhancedTaskListView {...defaultProps} />
      </DragDropProvider>
    );

    // 检查是否渲染了车辆调度面板
    expect(screen.getByText('车辆调度面板')).toBeInTheDocument();
    
    // 检查是否渲染了任务列表
    expect(screen.getByText('任务列表 (2)')).toBeInTheDocument();
    
    // 检查是否渲染了样式配置按钮
    expect(screen.getByText('样式配置')).toBeInTheDocument();
  });

  it('should display unassigned vehicles in the dispatch panel', () => {
    render(
      <DragDropProvider>
        <EnhancedTaskListView {...defaultProps} />
      </DragDropProvider>
    );

    // 检查未分配车辆数量显示
    expect(screen.getByText('未分配车辆: 1')).toBeInTheDocument();
    
    // 检查待调度车辆分组
    expect(screen.getByText('待调度')).toBeInTheDocument();
  });

  it('should display tasks with their information', () => {
    render(
      <DragDropProvider>
        <EnhancedTaskListView {...defaultProps} />
      </DragDropProvider>
    );

    // 检查任务信息是否显示
    expect(screen.getByText('T001')).toBeInTheDocument();
    expect(screen.getByText('T002')).toBeInTheDocument();
    expect(screen.getByText('测试工地1')).toBeInTheDocument();
    expect(screen.getByText('测试工地2')).toBeInTheDocument();
  });

  it('should handle style configuration modal', () => {
    render(
      <DragDropProvider>
        <EnhancedTaskListView {...defaultProps} />
      </DragDropProvider>
    );

    // 点击样式配置按钮
    const styleButton = screen.getByText('样式配置');
    fireEvent.click(styleButton);

    // 检查是否打开了配置模态框
    // 注意：这里可能需要根据实际的模态框实现来调整测试
  });

  it('should display vehicle status groups correctly', () => {
    const vehiclesWithDifferentStatuses: Vehicle[] = [
      ...mockVehicles,
      {
        id: 'vehicle-3',
        vehicleNumber: 'V003',
        status: 'outbound',
        operationalStatus: 'normal',
        currentProductionStatus: 'unloading',
      } as Vehicle,
      {
        id: 'vehicle-4',
        vehicleNumber: 'V004',
        status: 'returned',
        operationalStatus: 'normal',
        currentProductionStatus: 'unloaded',
      } as Vehicle,
    ];

    render(
      <DragDropProvider>
        <EnhancedTaskListView 
          {...defaultProps} 
          vehicles={vehiclesWithDifferentStatuses}
        />
      </DragDropProvider>
    );

    // 检查不同状态的车辆分组
    expect(screen.getByText('待调度')).toBeInTheDocument();
    expect(screen.getByText('运输中')).toBeInTheDocument();
    expect(screen.getByText('已返回')).toBeInTheDocument();
  });

  it('should show empty state when no unassigned vehicles', () => {
    const assignedVehicles: Vehicle[] = [
      {
        id: 'vehicle-1',
        vehicleNumber: 'V001',
        status: 'dispatched',
        operationalStatus: 'normal',
        currentProductionStatus: 'loading',
        assignedTaskId: 'task-1',
      } as Vehicle,
    ];

    render(
      <DragDropProvider>
        <EnhancedTaskListView 
          {...defaultProps} 
          vehicles={assignedVehicles}
        />
      </DragDropProvider>
    );

    // 检查空状态显示
    expect(screen.getByText('所有车辆已分配')).toBeInTheDocument();
    expect(screen.getByText('拖拽车辆到任务进行重新分配')).toBeInTheDocument();
  });
});

describe('DragDropProvider Context', () => {
  it('should provide drag drop context to children', () => {
    const TestComponent = () => {
      // 这里可以添加对 useDragDropContext 的测试
      return <div>Context Test</div>;
    };

    render(
      <DragDropProvider>
        <TestComponent />
      </DragDropProvider>
    );

    expect(screen.getByText('Context Test')).toBeInTheDocument();
  });
});

// 性能测试
describe('Performance Tests', () => {
  it('should render large number of tasks efficiently', () => {
    const largeTasks = Array.from({ length: 100 }, (_, i) => ({
      id: `task-${i}`,
      taskNumber: `T${i.toString().padStart(3, '0')}`,
      constructionSite: `测试工地${i}`,
      strength: 'C30',
      dispatchStatus: 'InProgress',
      requiredVolume: 100,
      completedVolume: 50,
      productionLines: ['L1'],
    })) as Task[];

    const startTime = performance.now();
    
    render(
      <DragDropProvider>
        <EnhancedTaskListView 
          {...defaultProps} 
          filteredTasks={largeTasks}
        />
      </DragDropProvider>
    );

    const endTime = performance.now();
    const renderTime = endTime - startTime;

    // 渲染时间应该在合理范围内（比如小于1000ms）
    expect(renderTime).toBeLessThan(1000);
    
    // 检查是否正确显示任务数量
    expect(screen.getByText('任务列表 (100)')).toBeInTheDocument();
  });

  it('should render large number of vehicles efficiently', () => {
    const largeVehicles = Array.from({ length: 50 }, (_, i) => ({
      id: `vehicle-${i}`,
      vehicleNumber: `V${i.toString().padStart(3, '0')}`,
      status: 'pending',
      operationalStatus: 'normal',
      currentProductionStatus: 'loading',
    })) as Vehicle[];

    const startTime = performance.now();
    
    render(
      <DragDropProvider>
        <EnhancedTaskListView 
          {...defaultProps} 
          vehicles={largeVehicles}
        />
      </DragDropProvider>
    );

    const endTime = performance.now();
    const renderTime = endTime - startTime;

    // 渲染时间应该在合理范围内
    expect(renderTime).toBeLessThan(1000);
    
    // 检查是否正确显示车辆数量
    expect(screen.getByText('未分配车辆: 50')).toBeInTheDocument();
  });
});
