// src/components/sections/task-list/EnhancedTaskCardView.tsx
'use client';

import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { Virtuoso } from 'react-virtuoso';
import { cn } from '@/lib/utils';
// Removed: import { DragDropProvider } from '@/contexts/DragDropContext'; // This was causing the error
import { OptimizedTaskCard, VirtualizedRow } from './cards/OptimizedTaskCard';
import { TaskCardConfigModal } from './cards/TaskCardConfigModal';
import { TaskCardConfig, defaultTaskCardConfig } from '@/types/taskCardConfig';
import { PerformanceMonitorPanel } from './cards/PerformanceMonitorPanel';
import { useCardPerformance } from './cards/useCardPerformance';
import { LayoutGrid } from 'lucide-react';
import { TaskGroupHeader } from './components/task-group-header';
import { groupTasks } from '@/utils/task-grouping';
import type { Task, Vehicle, TaskListStoredSettings, VehicleDisplayMode } from '@/types';


interface CardConfig {
  size: 'small' | 'medium' | 'large' | 'extra-large';
  layout: 'compact' | 'standard' | 'detailed' | 'minimal';
  theme: 'default' | 'modern' | 'glass' | 'gradient' | 'dark';
  spacing: 'tight' | 'normal' | 'loose';
  borderRadius: 'none' | 'small' | 'medium' | 'large' | 'full';
  shadow: 'none' | 'small' | 'medium' | 'large' | 'glow';
  animation: 'none' | 'subtle' | 'smooth' | 'bouncy';
  columns: 'auto' | '1' | '2' | '3' | '4' | '5' | '6';
}

interface EnhancedTaskCardViewProps {
  filteredTasks: Task[];
  vehicles: Vehicle[];
  settings: TaskListStoredSettings;
  productionLineCount: number;
  vehicleDisplayMode: VehicleDisplayMode;
  taskStatusFilter: string; // This prop might be unused now
  cardConfig: CardConfig;
  onCardConfigChange: (config: CardConfig) => void;
  onOpenCardConfigModal: () => void;
  onCancelVehicleDispatch: (vehicleId: string) => void;
  onOpenDeliveryOrderDetailsForVehicle: (vehicleId: string, taskId: string) => void;
  onOpenVehicleCardContextMenu: (event: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
  onTaskContextMenu: (event: React.MouseEvent, task: Task) => void; // Changed taskId to task
  onTaskDoubleClick: (task: Task) => void;
  onOpenStyleEditor: () => void;
  onDropVehicleOnLine?: (vehicle: Vehicle, taskId: string, lineId: string) => void; // For react-dnd in EnhancedTaskCard
   // For react-dnd, when a vehicle from dispatch panel is dropped on EnhancedTaskCard
  onDropVehicleFromPanelOnTaskCard?: (vehicle: Vehicle, taskId: string) => void;
  // 分组相关属性
  groupConfig?: any; // TaskGroupConfig
  onToggleGroupCollapse?: (groupKey: string) => void;
  onCancelGrouping?: () => void;
  // 卡片配置
  taskCardConfig?: TaskCardConfig;
  onTaskCardConfigChange?: (config: TaskCardConfig) => void;
}

export const EnhancedTaskCardView: React.FC<EnhancedTaskCardViewProps> = ({
  filteredTasks,
  vehicles,
  settings,
  productionLineCount, // Potentially unused
  cardConfig,
  onCardConfigChange,
  onOpenCardConfigModal,
  vehicleDisplayMode,
  taskStatusFilter, // Potentially unused
  onCancelVehicleDispatch,
  onOpenDeliveryOrderDetailsForVehicle,
  onOpenVehicleCardContextMenu,
  onTaskContextMenu,
  onTaskDoubleClick,
  onOpenStyleEditor,
  onDropVehicleFromPanelOnTaskCard, // Added prop consumption
  onDropVehicleOnLine, // Added prop consumption
  groupConfig,
  onToggleGroupCollapse,
  onCancelGrouping,
  taskCardConfig = defaultTaskCardConfig,
  onTaskCardConfigChange,
}) => {

  const [performanceMonitorOpen, setPerformanceMonitorOpen] = useState(false);
  const [taskCardConfigModalOpen, setTaskCardConfigModalOpen] = useState(false);
  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1280);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 性能优化 Hook
  const {
    containerRef,
    performanceConfig,
    // measureRender, // If not used, can be removed
    shouldUseVirtualScroll,
    getVirtualScrollConfig,
  } = useCardPerformance({
    componentName: 'EnhancedTaskCardView',
    enableMonitoring: process.env.NODE_ENV === 'development',
  });

  // 动态计算列数 - 基于容器宽度和卡片尺寸
  const [containerWidth, setContainerWidth] = React.useState(0);

  // 监听容器宽度变化
  React.useEffect(() => {
    const updateWidth = () => {
      if (containerRef.current) {
        const width = containerRef.current.clientWidth;
        setContainerWidth(width);
      }
    };

    // 多次尝试获取宽度，确保DOM完全渲染
    const timers: NodeJS.Timeout[] = [];
    timers.push(setTimeout(updateWidth, 50));
    timers.push(setTimeout(updateWidth, 200));
    timers.push(setTimeout(updateWidth, 500));

    const resizeObserver = new ResizeObserver(() => {
      // 防抖处理
      timers.forEach(clearTimeout);
      timers.length = 0;
      timers.push(setTimeout(updateWidth, 50));
    });

    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      timers.forEach(clearTimeout);
      resizeObserver.disconnect();
    };
  }, [containerRef]);

  // 动态计算卡片尺寸
  const calculateCardDimensions = useMemo(() => {
    const baseConfig = taskCardConfig || {};

    // 计算内容高度需求
    const calculateContentHeight = () => {
      let height = 0;

      // 顶部区域高度
      if (baseConfig.areas?.top?.visible !== false) {
        height += baseConfig.style?.spacing === 'tight' ? 40 :
                 baseConfig.style?.spacing === 'loose' ? 60 : 50;
      }

      // 车辆区域高度（固定）
      height += 100; // 两行三列车辆区域

      // 内容区域高度
      if (baseConfig.areas?.content?.visible !== false) {
        const contentFields = Object.values(baseConfig.areas?.content?.fields || {}).filter(field => field?.visible !== false);
        const fieldsPerColumn = baseConfig.areas?.content?.layout === 'double' ? Math.ceil(contentFields.length / 2) : contentFields.length;
        height += fieldsPerColumn * (baseConfig.style?.spacing === 'tight' ? 20 :
                                   baseConfig.style?.spacing === 'loose' ? 32 : 24);
      }

      // 底部区域高度
      if (baseConfig.areas?.bottom?.visible !== false) {
        const bottomFields = Object.values(baseConfig.areas?.bottom?.fields || {}).filter(field => field?.visible !== false);
        const fieldsPerColumn = baseConfig.areas?.bottom?.layout === 'double' ? Math.ceil(bottomFields.length / 2) : bottomFields.length;
        height += fieldsPerColumn * (baseConfig.style?.spacing === 'tight' ? 18 :
                                   baseConfig.style?.spacing === 'loose' ? 28 : 22);
      }

      // 内边距
      height += baseConfig.style?.spacing === 'tight' ? 16 :
               baseConfig.style?.spacing === 'loose' ? 32 : 24;

      return Math.max(height, 280); // 最小高度
    };

    // 计算宽度需求
    const calculateWidth = () => {
      const isDoubleLayout = baseConfig.areas?.content?.layout === 'double' ||
                           baseConfig.areas?.bottom?.layout === 'double';

      // 基础宽度 - 减小基础宽度以支持更多列
      let width = isDoubleLayout ? 280 : 240;

      // 根据间距调整
      if (baseConfig.style?.spacing === 'tight') {
        width -= 20; // 减少调整幅度
      } else if (baseConfig.style?.spacing === 'loose') {
        width += 40; // 减少调整幅度
      }

      return Math.max(width, 220); // 降低最小宽度
    };

    const width = calculateWidth();
    const height = calculateContentHeight();

    return { width, height };
  }, [taskCardConfig]);

  // 获取网格列数 - 动态计算或使用响应式
  const gridColumns = useMemo(() => {
    if (cardConfig.columns !== 'auto') {
      return `grid-cols-${cardConfig.columns}`;
    }

    // 如果有容器宽度，进行精确计算
    if (containerWidth > 0) {
      const cardWidth = calculateCardDimensions.width;
      const gap = cardConfig.spacing === 'tight' ? 8 : cardConfig.spacing === 'loose' ? 24 : 16;
      const padding = 32; // 容器内边距

      const availableWidth = containerWidth - padding;
      const maxColumns = Math.floor((availableWidth + gap) / (cardWidth + gap));

      // 根据卡片宽度动态设置列数限制 - 优化阈值
      let maxAllowedColumns;
      if (cardWidth <= 240) {
        maxAllowedColumns = 8; // 小卡片
      } else if (cardWidth <= 280) {
        maxAllowedColumns = 6; // 中等卡片
      } else if (cardWidth <= 320) {
        maxAllowedColumns = 5; // 大卡片
      } else if (cardWidth <= 360) {
        maxAllowedColumns = 4; // 较大卡片
      } else {
        maxAllowedColumns = 3; // 超大卡片
      }

      const columns = Math.max(1, Math.min(maxAllowedColumns, maxColumns));
      return `grid-cols-${columns}`;
    }

    // 回退到响应式布局 - 优化阈值和列数
    const cardWidth = calculateCardDimensions.width;
    if (cardWidth <= 240) {
      return 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-8';
    } else if (cardWidth <= 280) {
      return 'grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6';
    } else if (cardWidth <= 320) {
      return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-5';
    } else if (cardWidth <= 360) {
      return 'grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-4';
    } else {
      return 'grid-cols-1 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-3';
    }
  }, [cardConfig.columns, cardConfig.spacing, containerWidth, calculateCardDimensions, taskCardConfig]);

  // 获取间距样式 - 使用 useMemo 缓存
  const spacingClass = useMemo(() => {
    switch (cardConfig.spacing) {
      case 'tight': return 'gap-2';
      case 'loose': return 'gap-6';
      default: return 'gap-4';
    }
  }, [cardConfig.spacing]);

  // 获取容器主题样式 - 使用 useMemo 缓存
  const containerTheme = useMemo(() => {
    switch (cardConfig.theme) {
      case 'modern':
        return 'bg-gradient-to-br from-slate-50 to-gray-100 dark:from-slate-900 dark:to-gray-800';
      case 'glass':
        return 'bg-white/70 backdrop-blur-sm dark:bg-gray-900/70';
      case 'gradient':
        return 'bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-blue-900/20 dark:via-gray-900 dark:to-purple-900/20';
      case 'dark':
        return 'bg-gray-900 text-white';
      default:
        return 'bg-background';
    }
  }, [cardConfig.theme]);

  // 计算每行的列数 - 用于虚拟滚动
  const columnsPerRow = useMemo(() => {
    if (cardConfig.columns !== 'auto') {
      return parseInt(cardConfig.columns);
    }

    // 优先使用容器宽度进行精确计算
    if (containerWidth > 0) {
      const cardWidth = calculateCardDimensions.width;
      const gap = cardConfig.spacing === 'tight' ? 8 : cardConfig.spacing === 'loose' ? 24 : 16;
      const padding = 32;

      const availableWidth = containerWidth - padding;
      const maxColumns = Math.floor((availableWidth + gap) / (cardWidth + gap));

      // 根据卡片宽度动态设置列数限制 - 与gridColumns保持一致
      let maxAllowedColumns;
      if (cardWidth <= 240) {
        maxAllowedColumns = 8; // 小卡片
      } else if (cardWidth <= 280) {
        maxAllowedColumns = 6; // 中等卡片
      } else if (cardWidth <= 320) {
        maxAllowedColumns = 5; // 大卡片
      } else if (cardWidth <= 360) {
        maxAllowedColumns = 4; // 较大卡片
      } else {
        maxAllowedColumns = 3; // 超大卡片
      }

      return Math.max(1, Math.min(maxAllowedColumns, maxColumns));
    }

    // 回退到基于窗口宽度的计算
    const cardWidth = calculateCardDimensions.width;
    const width = windowWidth;

    if (cardWidth <= 240) {
      // 小卡片
      if (width >= 1536) return 8; // 2xl
      if (width >= 1280) return 6; // xl
      if (width >= 1024) return 5; // lg
      if (width >= 768) return 4;  // md
      if (width >= 640) return 3;  // sm
      return 2; // 最小2列
    } else if (cardWidth <= 280) {
      // 中等卡片
      if (width >= 1536) return 6; // 2xl
      if (width >= 1280) return 5; // xl
      if (width >= 1024) return 4; // lg
      if (width >= 768) return 3;  // md
      if (width >= 640) return 2;  // sm
      return 2; // 最小2列
    } else if (cardWidth <= 320) {
      // 大卡片
      if (width >= 1536) return 5; // 2xl
      if (width >= 1280) return 4; // xl
      if (width >= 1024) return 3; // lg
      if (width >= 768) return 2;  // md
      if (width >= 640) return 2;  // sm
      return 1;
    } else if (cardWidth <= 360) {
      // 较大卡片
      if (width >= 1536) return 4; // 2xl
      if (width >= 1280) return 3; // xl
      if (width >= 1024) return 2; // lg
      if (width >= 768) return 2;  // md
      return 1;
    } else {
      // 超大卡片
      if (width >= 1536) return 3; // 2xl
      if (width >= 1280) return 2; // xl
      if (width >= 1024) return 2; // lg
      return 1;
    }
  }, [cardConfig.columns, cardConfig.spacing, containerWidth, windowWidth, calculateCardDimensions]);

  // 计算行数
  const totalRows = useMemo(() => {
    return Math.ceil(filteredTasks.length / columnsPerRow);
  }, [filteredTasks.length, columnsPerRow]);

  // 处理任务右键菜单 - 使用 useCallback 优化
  const handleTaskContextMenu = useCallback((e: React.MouseEvent, task: Task) => {
    onTaskContextMenu(e, task); // Pass the whole task object
  }, [onTaskContextMenu]);

  // 处理车辆双击 - 使用 useCallback 优化
  const handleVehicleDoubleClick = useCallback((vehicleId: string, taskId: string) => {
    onOpenDeliveryOrderDetailsForVehicle(vehicleId, taskId);
  }, [onOpenDeliveryOrderDetailsForVehicle]);

  // 缓存车辆分组，避免每次渲染时重新计算
  const vehiclesByTask = useMemo(() => {
    const map = new Map<string, Vehicle[]>();
    vehicles.forEach(vehicle => {
      if (vehicle.assignedTaskId) {
        if (!map.has(vehicle.assignedTaskId)) {
          map.set(vehicle.assignedTaskId, []);
        }
        map.get(vehicle.assignedTaskId)!.push(vehicle);
      }
    });
    return map;
  }, [vehicles]);

  // 分组数据计算
  const taskGroups = useMemo(() => {
    if (!groupConfig || !groupConfig.enabled) {
      return [{
        key: 'all',
        label: '所有任务',
        tasks: filteredTasks,
        collapsed: false,
      }];
    }
    return groupTasks(filteredTasks, groupConfig);
  }, [filteredTasks, groupConfig]);

  // 渲染单行卡片 - 用于虚拟滚动，使用性能优化
  const renderRow = useCallback((index: number) => {
    return (
      <VirtualizedRow
        key={index}
        index={index}
        tasks={filteredTasks}
        vehicles={vehicles}
        config={taskCardConfig}
        size={cardConfig.size}
        columnsPerRow={columnsPerRow}
        vehicleDisplayMode={vehicleDisplayMode}
        inTaskVehicleCardStyles={settings.inTaskVehicleCardStyles}
        density={settings.density}
        onTaskContextMenu={handleTaskContextMenu}
        onTaskDoubleClick={onTaskDoubleClick}
        onDropVehicleOnLine={onDropVehicleOnLine}
        onCancelDispatch={onCancelVehicleDispatch}
        onOpenStyleEditor={onOpenStyleEditor}
        onOpenDeliveryOrderDetails={handleVehicleDoubleClick}
        onOpenVehicleContextMenu={onOpenVehicleCardContextMenu}
        gridColumns={gridColumns}
        spacingClass={spacingClass}
      />
    );
  }, [
    filteredTasks,
    vehicles,
    taskCardConfig,
    columnsPerRow,
    vehicleDisplayMode,
    settings.inTaskVehicleCardStyles,
    settings.density,
    handleTaskContextMenu,
    onTaskDoubleClick,
    onDropVehicleOnLine,
    onCancelVehicleDispatch,
    onOpenStyleEditor,
    handleVehicleDoubleClick,
    onOpenVehicleCardContextMenu,
    gridColumns,
    spacingClass,
  ]);

  // 空状态显示
  if (filteredTasks.length === 0) {
    return (
      // Removed the DragDropProvider wrapper as it's global now
        <div
          ref={containerRef}
          className={cn(
            "flex flex-col h-full transition-all duration-300",
            containerTheme
          )}
        >
          <div className="flex items-center justify-between p-4 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="flex items-center gap-2">
              <LayoutGrid className="w-5 h-5 text-muted-foreground" />
              <span className="font-medium">任务卡片 (0)</span>
            </div>
          </div>

          <div className={cn(
            "flex flex-col items-center justify-center flex-1 text-muted-foreground",
            spacingClass
          )}>
            <div className="text-center space-y-4">
              <div className="w-24 h-24 mx-auto bg-muted rounded-full flex items-center justify-center">
                <LayoutGrid className="w-12 h-12 text-muted-foreground" />
              </div>
              <div>
                <h3 className="text-lg font-medium mb-2">暂无任务</h3>
                <p className="text-sm text-muted-foreground">
                  当前筛选条件下没有找到任务
                </p>
              </div>
            </div>
          </div>
        </div>
    );
  }

  // 获取虚拟滚动配置
  const virtualScrollConfig = getVirtualScrollConfig();
  const useVirtualScroll = shouldUseVirtualScroll(filteredTasks.length);

  return (
    // Removed the DragDropProvider wrapper
      <div
        ref={containerRef}
        className={cn(
          "flex flex-col h-full transition-all duration-300",
          containerTheme,
          // 性能优化类
          "transform-gpu"
        )}
        style={{
          // 启用 CSS 包含以提升性能
          contain: 'layout style paint',
        }}
      >




        {/* 卡片网格 - 支持分组显示 */}
        <div className="flex-1 overflow-hidden">
          {groupConfig && groupConfig.enabled ? (
            // 分组模式
            <div className="overflow-auto h-full space-y-4 p-4">
              {taskGroups.map((group) => (
                <div key={group.key} className="space-y-2">
                  <TaskGroupHeader
                    group={group}
                    groupConfig={groupConfig}
                    onToggleCollapse={onToggleGroupCollapse}
                    onCancelGrouping={onCancelGrouping}
                  />
                  {!group.collapsed && group.tasks.length > 0 && (
                    <div className={cn("grid", gridColumns, spacingClass)}>
                      {group.tasks.map((task) => {
                        const taskVehicles = vehiclesByTask.get(task.id) || [];

                        return (
                          <OptimizedTaskCard
                            key={task.id}
                            task={task}
                            vehicles={taskVehicles}
                            config={taskCardConfig}
                            size={cardConfig.size}
                            vehicleDisplayMode={vehicleDisplayMode}
                            inTaskVehicleCardStyles={settings.inTaskVehicleCardStyles}
                            density={settings.density}
                            onTaskContextMenu={handleTaskContextMenu}
                            onTaskDoubleClick={onTaskDoubleClick}
                            onDropVehicleOnLine={onDropVehicleOnLine}
                            onCancelDispatch={onCancelVehicleDispatch}
                            onOpenStyleEditor={onOpenStyleEditor}
                            onOpenDeliveryOrderDetails={handleVehicleDoubleClick}
                            onOpenVehicleContextMenu={onOpenVehicleCardContextMenu}
                          />
                        );
                      })}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            // 非分组模式
            useVirtualScroll ? (
              <Virtuoso
                style={{ height: '100%' }}
                totalCount={totalRows}
                itemContent={renderRow}
                components={{
                  Item: ({ children, ...props }) => (
                    <div {...props} className={cn("w-full", spacingClass)}>
                      {children}
                    </div>
                  ),
                }}
                overscan={virtualScrollConfig.overscan}
                increaseViewportBy={virtualScrollConfig.increaseViewportBy}
              />
            ) : (
              <div className="overflow-auto h-full card-grid-performance p-4">
                <div className={cn("grid", gridColumns, spacingClass)}>
                  {filteredTasks.map((task) => {
                    const taskVehicles = vehiclesByTask.get(task.id) || [];

                    return (
                      <OptimizedTaskCard
                        key={task.id}
                        task={task}
                        vehicles={taskVehicles}
                        config={taskCardConfig}
                        size={cardConfig.size}
                        vehicleDisplayMode={vehicleDisplayMode}
                        inTaskVehicleCardStyles={settings.inTaskVehicleCardStyles}
                        density={settings.density}
                        onTaskContextMenu={handleTaskContextMenu}
                        onTaskDoubleClick={onTaskDoubleClick}
                        onDropVehicleOnLine={onDropVehicleOnLine}
                        onCancelDispatch={onCancelVehicleDispatch}
                        onOpenStyleEditor={onOpenStyleEditor}
                        onOpenDeliveryOrderDetails={handleVehicleDoubleClick}
                        onOpenVehicleContextMenu={onOpenVehicleCardContextMenu}
                      />
                    );
                  })}
                </div>
              </div>
            )
          )}
        </div>





        {/* 性能监控面板 */}
        <PerformanceMonitorPanel
          isVisible={performanceMonitorOpen}
          onClose={() => setPerformanceMonitorOpen(false)}
        />

        {/* 任务卡片配置模态框 */}
        <TaskCardConfigModal
          open={taskCardConfigModalOpen}
          onOpenChange={setTaskCardConfigModalOpen}
          config={taskCardConfig}
          onConfigChange={onTaskCardConfigChange || (() => {})}
          previewTask={filteredTasks[0]}
          previewVehicles={filteredTasks[0] ? vehiclesByTask.get(filteredTasks[0].id) || [] : []}
        />
      </div>
  );
};
